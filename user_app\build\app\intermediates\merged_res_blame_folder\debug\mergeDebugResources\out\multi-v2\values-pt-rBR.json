{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4937,7656,7975", "endColumns": "75,81,73", "endOffsets": "5008,7733,8044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6025", "endColumns": "144", "endOffsets": "6165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3222,3292,3357,4047,4712,4790,4907,5022,5077,5171,5257,5329,5419,5516,5573,5667,5718,5796,5907,5978,6048,6115,6181,6229,6313,6411,6461,6508,6573,6631,6694,6886,7051,7190,7255,7341,7416,7505,7587,7664,7733,7824,7897,8002,8088,8183,8236,8352,8402,8456,8523,8595,8668,8737,8812,8902,8972,9024", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3217,3287,3352,4042,4707,4785,4902,5017,5072,5166,5252,5324,5414,5511,5568,5662,5713,5791,5902,5973,6043,6110,6176,6224,6308,6406,6456,6503,6568,6626,6689,6881,7046,7185,7250,7336,7411,7500,7582,7659,7728,7819,7892,7997,8083,8178,8231,8347,8397,8451,8518,8590,8663,8732,8807,8897,8967,9019,9098"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,317,318,320,321,325,326,328,333,340,341,402,403,404,406,411,422,423,424,425,426,427,428,443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14910,14984,15067,15129,15203,15286,15349,15660,15714,15790,15863,16090,16274,16376,16530,16887,17176,18194,18438,18533,18605,18765,18853,18941,19111,19418,19510,19589,19688,19762,19848,19935,20018,20101,20204,20357,20599,20701,21475,21545,21610,23216,23881,23959,24076,24191,24246,24340,24426,24498,24588,24685,24742,25364,25415,25493,25604,25675,25745,25812,26343,26722,26806,26904,26954,27242,27307,27365,27428,27620,27785,28112,28177,29040,29115,29285,29367,29710,29779,29936,30568,31094,31180,37438,37491,37607,37758,38320,40232,40304,40377,40446,40521,40611,40681,41967", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "14979,15062,15124,15198,15281,15344,15403,15709,15785,15858,15937,16187,16371,16457,16603,16963,17255,18280,18528,18600,18692,18848,18936,19044,19188,19505,19584,19683,19757,19843,19930,20013,20096,20199,20272,20429,20696,20796,21540,21605,22295,23876,23954,24071,24186,24241,24335,24421,24493,24583,24680,24737,24831,25410,25488,25599,25670,25740,25807,25873,26386,26801,26899,26949,26996,27302,27360,27423,27615,27780,27919,28172,28258,29110,29199,29362,29439,29774,29865,30004,30668,31175,31270,37486,37602,37652,37807,38382,40299,40372,40441,40516,40606,40676,40728,42041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1066,1158,1237,1297,1387,1451,1522,1585,1660,1724,1778,1905,1963,2025,2079,2158,2299,2386,2468,2607,2690,2774,2913,3000,3080,3136,3187,3253,3327,3407,3494,3577,3650,3727,3796,3870,3972,4060,4137,4230,4326,4400,4480,4577,4629,4713,4779,4866,4954,5016,5080,5143,5211,5323,5434,5541,5651,5711,5766", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "268,349,427,511,606,695,796,916,997,1061,1153,1232,1292,1382,1446,1517,1580,1655,1719,1773,1900,1958,2020,2074,2153,2294,2381,2463,2602,2685,2769,2908,2995,3075,3131,3182,3248,3322,3402,3489,3572,3645,3722,3791,3865,3967,4055,4132,4225,4321,4395,4475,4572,4624,4708,4774,4861,4949,5011,5075,5138,5206,5318,5429,5536,5646,5706,5761,5838"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3292,3373,3451,3535,3630,4454,4555,4675,7824,8049,8519,8778,8838,8928,8992,9063,9126,9201,9265,9319,9446,9504,9566,9620,9699,9840,9927,10009,10148,10231,10315,10454,10541,10621,10677,10728,10794,10868,10948,11035,11118,11191,11268,11337,11411,11513,11601,11678,11771,11867,11941,12021,12118,12170,12254,12320,12407,12495,12557,12621,12684,12752,12864,12975,13082,13192,13252,13705", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,80,77,83,94,88,100,119,80,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,81,138,82,83,138,86,79,55,50,65,73,79,86,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,111,110,106,109,59,54,76", "endOffsets": "318,3368,3446,3530,3625,3714,4550,4670,4751,7883,8136,8593,8833,8923,8987,9058,9121,9196,9260,9314,9441,9499,9561,9615,9694,9835,9922,10004,10143,10226,10310,10449,10536,10616,10672,10723,10789,10863,10943,11030,11113,11186,11263,11332,11406,11508,11596,11673,11766,11862,11936,12016,12113,12165,12249,12315,12402,12490,12552,12616,12679,12747,12859,12970,13077,13187,13247,13302,13777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,459,460,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4756,4851,7460,7557,7738,8598,8681,13307,13398,13564,13636,13933,14018,14194,43569,43645,43712", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4846,4932,7552,7651,7819,8676,8773,13393,13480,13631,13700,14013,14103,14265,43640,43707,43820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3061,3170", "endColumns": "108,121", "endOffsets": "3165,3287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,314,315,323,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15942,16192,16462,16608,16694,16765,17260,17333,17473,17535,17603,17673,17733,17794,17868,17991,18060,18285,19193,19258,20277,20434,20513,24908,25014,26198,27001,27049,27178,27924,27986,28740,28827,29533,29629", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "16003,16269,16525,16689,16760,16821,17328,17395,17530,17598,17668,17728,17789,17863,17927,18055,18118,18358,19253,19341,20352,20508,20594,25009,25100,26243,27044,27119,27237,27981,28050,28822,28911,29624,29705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5013,5118,5266,5393,5501,5668,5798,5920,6170,6340,6448,6612,6742,6899,7056,7125,7191", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "5113,5261,5388,5496,5663,5793,5915,6020,6335,6443,6607,6737,6894,7051,7120,7186,7270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,14108", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,14189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14371,14458,14566,14633,14701,14767,14841", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "14453,14561,14628,14696,14762,14836,14905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,324,419,633,680,751,852,926,1146,1206,1298,1370,1427,1491,1811,1883,1950,2004,2061,2145,2277,2391,2449,2538,2619,2708,2774,2875,3178,3255,3333,3396,3457,3519,3580,3654,3737,3837,3938,4028,4131,4228,4303,4382,4468,4682,4893,5008,5134,5190,5842,5907", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,64,54", "endOffsets": "166,319,414,628,675,746,847,921,1141,1201,1293,1365,1422,1486,1806,1878,1945,1999,2056,2140,2272,2386,2444,2533,2614,2703,2769,2870,3173,3250,3328,3391,3452,3514,3575,3649,3732,3832,3933,4023,4126,4223,4298,4377,4463,4677,4888,5003,5129,5185,5837,5902,5957"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,309,310,311,312,313,319,322,327,329,330,331,332,335,337,338,342,346,347,349,351,359,379,380,381,382,383,400,407,408,409,410,412,413,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20801,20917,21070,21261,22300,22347,22418,22519,22593,22813,22873,24836,25105,25300,25878,26583,26655,27124,28055,28263,28347,28479,28593,28651,29204,29444,29870,30009,30110,30413,30490,30729,30907,30968,31275,31830,31904,32079,32285,33349,35239,35342,35439,35514,35593,37132,37812,38023,38138,38264,38387,39039,40733", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,64,54", "endOffsets": "20912,21065,21160,21470,22342,22413,22514,22588,22808,22868,22960,24903,25157,25359,26193,26650,26717,27173,28107,28342,28474,28588,28646,28735,29280,29528,29931,30105,30408,30485,30563,30787,30963,31025,31331,31899,31982,32174,32381,33434,35337,35434,35509,35588,35674,37341,38018,38133,38259,38315,39034,39099,40783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3719,3816,3918,4017,4117,4224,4334,14270", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3811,3913,4012,4112,4219,4329,4449,14366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "84,464", "startColumns": "4,4", "startOffsets": "8458,43993", "endColumns": "60,79", "endOffsets": "8514,44068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,215,339,395,459,551,611,674,752,870,1039,1308,1607,1683,1756,1844,1931,2045,2178,2246,2317,2480,2550,2607,2684,2763,2890,3016,3130,3218,3295,3379,3450", "endColumns": "76,82,123,55,63,91,59,62,77,117,168,268,298,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,113,87,76,83,70,163", "endOffsets": "127,210,334,390,454,546,606,669,747,865,1034,1303,1602,1678,1751,1839,1926,2040,2173,2241,2312,2475,2545,2602,2679,2758,2885,3011,3125,3213,3290,3374,3445,3609"}, "to": {"startLines": "170,172,316,334,339,401,414,415,416,417,418,419,420,435,436,437,438,439,440,441,442,445,446,447,448,449,450,451,452,453,454,455,456,457", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15408,15577,28916,30673,31030,37346,39104,39164,39227,39305,39423,39592,39861,41257,41333,41406,41494,41581,41695,41828,41896,42103,42266,42336,42393,42470,42549,42676,42802,42916,43004,43081,43165,43236", "endColumns": "76,82,123,55,63,91,59,62,77,117,168,268,298,75,72,87,86,113,132,67,70,162,69,56,76,78,126,125,113,87,76,83,70,163", "endOffsets": "15480,15655,29035,30724,31089,37433,39159,39222,39300,39418,39587,39856,40155,41328,41401,41489,41576,41690,41823,41891,41962,42261,42331,42388,42465,42544,42671,42797,42911,42999,43076,43160,43231,43395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,147,243,329,412,494,632,727,828,919,1034,1205,1388,1528,1620,1726,1821,1920,2088,2198,2298,2576,2689,2885,2986,3047,3113,3195,3295,3396,3506,3719,3787,3856,3935,4009,4094,4156,4235,4306,4374,4489,4576,4683,4780,4855,4937,5006,5097,5157,5262,5369,5473,5603,5663,5763,5871,5942,6043,6115,6200,6272,6384,6497,6584", "endColumns": "91,95,85,82,81,137,94,100,90,114,170,182,139,91,105,94,98,167,109,99,277,112,195,100,60,65,81,99,100,109,212,67,68,78,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,70,100,71,84,71,111,112,86,56", "endOffsets": "142,238,324,407,489,627,722,823,914,1029,1200,1383,1523,1615,1721,1816,1915,2083,2193,2293,2571,2684,2880,2981,3042,3108,3190,3290,3391,3501,3714,3782,3851,3930,4004,4089,4151,4230,4301,4369,4484,4571,4678,4775,4850,4932,5001,5092,5152,5257,5364,5468,5598,5658,5758,5866,5937,6038,6110,6195,6267,6379,6492,6579,6636"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,336,343,344,345,348,350,352,353,354,355,356,357,358,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,405,421,430,431,432,433,434,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15485,21165,22965,23051,23134,25162,26248,26391,26492,30792,31336,31507,31690,31987,32179,32386,32481,32580,32748,32858,32958,33236,33439,33635,33736,33797,33863,33945,34045,34146,34256,34469,34537,34606,34685,34759,34844,34906,34985,35056,35124,35679,35766,35873,35970,36045,36127,36196,36287,36347,36452,36559,36663,36793,36853,36953,37061,37657,40160,40788,40873,40945,41057,41170,42046", "endColumns": "91,95,85,82,81,137,94,100,90,114,170,182,139,91,105,94,98,167,109,99,277,112,195,100,60,65,81,99,100,109,212,67,68,78,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,70,100,71,84,71,111,112,86,56", "endOffsets": "15572,21256,23046,23129,23211,25295,26338,26487,26578,30902,31502,31685,31825,32074,32280,32476,32575,32743,32853,32953,33231,33344,33630,33731,33792,33858,33940,34040,34141,34251,34464,34532,34601,34680,34754,34839,34901,34980,35051,35119,35234,35761,35868,35965,36040,36122,36191,36282,36342,36447,36554,36658,36788,36848,36948,37056,37127,37753,40227,40868,40940,41052,41165,41252,42098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "71,78,146,150,458,462,463", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7275,7888,13485,13782,43400,43825,43912", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "7340,7970,13559,13928,43564,43907,43988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16008,16826,16968,17030,17112,17400,17932,18123,18363,18697,19049,19346", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "16085,16882,17025,17107,17171,17468,17986,18189,18433,18760,19106,19413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7345,8141,8240,8352", "endColumns": "114,98,111,105", "endOffsets": "7455,8235,8347,8453"}}]}]}