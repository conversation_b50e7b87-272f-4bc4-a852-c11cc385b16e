{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3059,3173", "endColumns": "113,121", "endOffsets": "3168,3290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5034,5142,5305,5436,5544,5705,5838,5960,6230,6422,6531,6696,6828,6993,7150,7217,7286", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "5137,5300,5431,5539,5700,5833,5955,6065,6417,6526,6691,6823,6988,7145,7212,7281,7366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,238,330,414,497,631,725,819,911,1033,1205,1394,1550,1649,1749,1846,1946,2147,2252,2353,2624,2739,2932,3026,3087,3153,3234,3340,3440,3545,3763,3831,3902,3978,4050,4134,4209,4303,4374,4442,4559,4647,4767,4866,4943,5038,5107,5195,5256,5362,5473,5573,5712,5781,5878,5982,6053,6163,6244,6334,6401,6518,6624,6709", "endColumns": "88,93,91,83,82,133,93,93,91,121,171,188,155,98,99,96,99,200,104,100,270,114,192,93,60,65,80,105,99,104,217,67,70,75,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,70,109,80,89,66,116,105,84,52", "endOffsets": "139,233,325,409,492,626,720,814,906,1028,1200,1389,1545,1644,1744,1841,1941,2142,2247,2348,2619,2734,2927,3021,3082,3148,3229,3335,3435,3540,3758,3826,3897,3973,4045,4129,4204,4298,4369,4437,4554,4642,4762,4861,4938,5033,5102,5190,5251,5357,5468,5568,5707,5776,5873,5977,6048,6158,6239,6329,6396,6513,6619,6704,6757"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15648,21536,23383,23475,23559,25688,26758,26900,26994,31631,32197,32369,32558,32872,33081,33287,33384,33484,33685,33790,33891,34162,34367,34560,34654,34715,34781,34862,34968,35068,35173,35391,35459,35530,35606,35678,35762,35837,35931,36002,36070,36631,36719,36839,36938,37015,37110,37179,37267,37328,37434,37545,37645,37784,37853,37950,38054,38632,41274,41925,42015,42082,42199,42305,43224", "endColumns": "88,93,91,83,82,133,93,93,91,121,171,188,155,98,99,96,99,200,104,100,270,114,192,93,60,65,80,105,99,104,217,67,70,75,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,70,109,80,89,66,116,105,84,52", "endOffsets": "15732,21625,23470,23554,23637,25817,26847,26989,27081,31748,32364,32553,32709,32966,33176,33379,33479,33680,33785,33886,34157,34272,34555,34649,34710,34776,34857,34963,35063,35168,35386,35454,35525,35601,35673,35757,35832,35926,35997,36065,36182,36714,36834,36933,37010,37105,37174,37262,37323,37429,37540,37640,37779,37848,37945,38049,38120,38737,41350,42010,42077,42194,42300,42385,43272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4956,7749,8082", "endColumns": "77,82,77", "endOffsets": "5029,7827,8155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8576,45218", "endColumns": "60,77", "endOffsets": "8632,45291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3295,3376,3455,3542,3643,4471,4575,4697,7921,8160,8637,8890,8953,9042,9106,9175,9238,9312,9376,9432,9550,9608,9670,9726,9806,9945,10034,10116,10257,10338,10418,10569,10659,10739,10795,10851,10917,10996,11078,11166,11255,11329,11406,11476,11555,11655,11739,11823,11915,12015,12089,12170,12272,12325,12410,12477,12570,12659,12721,12785,12848,12916,13029,13136,13240,13341,13401,13864", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3371,3450,3537,3638,3734,4570,4692,4773,7981,8250,8713,8948,9037,9101,9170,9233,9307,9371,9427,9545,9603,9665,9721,9801,9940,10029,10111,10252,10333,10413,10564,10654,10734,10790,10846,10912,10991,11073,11161,11250,11324,11401,11471,11550,11650,11734,11818,11910,12010,12084,12165,12267,12320,12405,12472,12565,12654,12716,12780,12843,12911,13024,13131,13235,13336,13396,13456,13942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,209,337,405,469,568,626,682,768,866,1044,1326,1624,1712,1785,1874,1971,2099,2244,2315,2386,2550,2615,2680,2753,2832,2977,3099,3209,3299,3387,3472,3548", "endColumns": "74,78,127,67,63,98,57,55,85,97,177,281,297,87,72,88,96,127,144,70,70,163,64,64,72,78,144,121,109,89,87,84,75,166", "endOffsets": "125,204,332,400,464,563,621,677,763,861,1039,1321,1619,1707,1780,1869,1966,2094,2239,2310,2381,2545,2610,2675,2748,2827,2972,3094,3204,3294,3382,3467,3543,3710"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15573,15737,29597,31482,31890,38307,40218,40276,40332,40418,40516,40694,40976,42390,42478,42551,42640,42737,42865,43010,43081,43277,43441,43506,43571,43644,43723,43868,43990,44100,44190,44278,44363,44439", "endColumns": "74,78,127,67,63,98,57,55,85,97,177,281,297,87,72,88,96,127,144,70,70,163,64,64,72,78,144,121,109,89,87,84,75,166", "endOffsets": "15643,15811,29720,31545,31949,38401,40271,40327,40413,40511,40689,40971,41269,42473,42546,42635,42732,42860,43005,43076,43147,43436,43501,43566,43639,43718,43863,43985,44095,44185,44273,44358,44434,44601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "28830", "endColumns": "84", "endOffsets": "28910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7371,7986,13640,13947,44606,45048,45136", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "7436,8077,13716,14089,44770,45131,45213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14532,14619,14723,14789,14856,14922,15004", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "14614,14718,14784,14851,14917,14999,15067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16210,17082,17227,17289,17370,17658,18193,18386,18643,18977,19329,19627", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "16298,17138,17284,17365,17429,17728,18249,18462,18713,19040,19386,19694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3383,3459,3527,4238,4929,5008,5138,5245,5300,5398,5490,5575,5685,5798,5857,5943,5994,6074,6184,6262,6335,6402,6468,6516,6597,6703,6749,6799,6870,6928,6992,7192,7339,7484,7556,7642,7726,7821,7911,8009,8087,8180,8261,8361,8446,8543,8598,8718,8769,8831,8907,8977,9056,9126,9197,9291,9366,9419", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3378,3454,3522,4233,4924,5003,5133,5240,5295,5393,5485,5570,5680,5793,5852,5938,5989,6069,6179,6257,6330,6397,6463,6511,6592,6698,6744,6794,6865,6923,6987,7187,7334,7479,7551,7637,7721,7816,7906,8004,8082,8175,8256,8356,8441,8538,8593,8713,8764,8826,8902,8972,9051,9121,9192,9286,9361,9414,9486"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15072,15147,15231,15293,15369,15454,15514,15816,15874,15968,16049,16303,16509,16622,16778,17143,17434,18467,18718,18813,18885,19045,19133,19221,19391,19699,19791,19870,19969,20047,20145,20239,20330,20428,20546,20699,20970,21073,21836,21912,21980,23642,24333,24412,24542,24649,24704,24802,24894,24979,25089,25202,25261,25886,25937,26017,26127,26205,26278,26345,26852,27226,27307,27413,27459,27789,27860,27918,27982,28182,28329,28672,28744,29725,29809,29986,30076,30484,30562,30725,31382,31954,32039,38406,38461,38581,38742,39306,41355,41425,41504,41574,41645,41739,41814,43152", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "15142,15226,15288,15364,15449,15509,15568,15869,15963,16044,16138,16411,16617,16705,16851,17222,17513,18563,18808,18880,18972,19128,19216,19324,19468,19786,19865,19964,20042,20140,20234,20325,20423,20541,20617,20786,21068,21164,21907,21975,22686,24328,24407,24537,24644,24699,24797,24889,24974,25084,25197,25256,25342,25932,26012,26122,26200,26273,26340,26406,26895,27302,27408,27454,27504,27855,27913,27977,28177,28324,28469,28739,28825,29804,29899,30071,30169,30557,30650,30801,31477,32034,32131,38456,38576,38627,38799,39377,41420,41499,41569,41640,41734,41809,41862,43219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,331", "endColumns": "58,46,169,135", "endOffsets": "109,156,326,462"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "45296,45355,45402,45572", "endColumns": "58,46,169,135", "endOffsets": "45350,45397,45567,45703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3739,3838,3940,4040,4138,4245,4351,14431", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3833,3935,4035,4133,4240,4346,4466,14527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7441,8255,8356,8471", "endColumns": "106,100,114,104", "endOffsets": "7543,8351,8466,8571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16143,16416,16710,16856,16949,17021,17518,17591,17733,17795,17863,17934,17994,18055,18129,18254,18323,18568,19473,19546,20622,20791,20877,25423,25540,26708,27509,27569,27725,28474,28544,29397,29508,30277,30381", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "16205,16504,16773,16944,17016,17077,17586,17653,17790,17858,17929,17989,18050,18124,18188,18318,18381,18638,19541,19622,20694,20872,20965,25535,25626,26753,27564,27652,27784,28539,28608,29503,29592,30376,30479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4778,4874,7548,7646,7832,8718,8797,13461,13553,13721,13794,14094,14180,14354,44775,44857,44927", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4869,4951,7641,7744,7916,8792,8885,13548,13635,13789,13859,14175,14266,14426,44852,44922,45043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,14271", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6070", "endColumns": "159", "endOffsets": "6225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,327,422,628,674,746,849,924,1165,1227,1320,1396,1453,1517,1814,1887,1954,2022,2081,2169,2304,2416,2474,2563,2645,2748,2818,2918,3239,3316,3394,3475,3550,3612,3673,3745,3831,3941,4047,4137,4246,4340,4416,4495,4581,4763,4968,5077,5203,5265,6036,6101", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,64,57", "endOffsets": "164,322,417,623,669,741,844,919,1160,1222,1315,1391,1448,1512,1809,1882,1949,2017,2076,2164,2299,2411,2469,2558,2640,2743,2813,2913,3234,3311,3389,3470,3545,3607,3668,3740,3826,3936,4042,4132,4241,4335,4411,4490,4576,4758,4963,5072,5198,5260,6031,6096,6154"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21169,21283,21441,21630,22691,22737,22809,22912,22987,23228,23290,25347,25631,25822,26411,27086,27159,27657,28613,28915,29003,29138,29250,29308,29904,30174,30655,30806,30906,31227,31304,31550,31753,31828,32136,32714,32786,32971,33181,34277,36187,36296,36390,36466,36545,38125,38804,39009,39118,39244,39382,40153,41867", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,64,57", "endOffsets": "21278,21436,21531,21831,22732,22804,22907,22982,23223,23285,23378,25418,25683,25881,26703,27154,27221,27720,28667,28998,29133,29245,29303,29392,29981,30272,30720,30901,31222,31299,31377,31626,31823,31885,32192,32781,32867,33076,33282,34362,36291,36385,36461,36540,36626,38302,39004,39113,39239,39301,40148,40213,41920"}}]}]}