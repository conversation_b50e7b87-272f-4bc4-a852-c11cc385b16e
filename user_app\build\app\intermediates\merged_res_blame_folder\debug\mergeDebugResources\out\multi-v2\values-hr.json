{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,221,288,374,441,502,587,651,717,785,864,924,985,1059,1123,1191,1254,1328,1394,1474,1553,1634,1727,1827,1912,1963,2011,2089,2153,2218,2289,2389,2474,2565", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "114,216,283,369,436,497,582,646,712,780,859,919,980,1054,1118,1186,1249,1323,1389,1469,1548,1629,1722,1822,1907,1958,2006,2084,2148,2213,2284,2384,2469,2560,2651"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,277,288,289,291,298,299,309,310,318,319", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15303,15557,15851,16002,16088,16155,16659,16744,16876,16942,17010,17089,17149,17210,17284,17406,17474,17715,18643,18709,19740,19898,19979,24358,24458,25598,26399,26447,26579,27291,27356,28155,28255,28966,29057", "endColumns": "63,101,66,85,66,60,84,63,65,67,78,59,60,73,63,67,62,73,65,79,78,80,92,99,84,50,47,77,63,64,70,99,84,90,90", "endOffsets": "15362,15654,15913,16083,16150,16211,16739,16803,16937,17005,17084,17144,17205,17279,17343,17469,17532,17784,18704,18784,19814,19974,20067,24453,24538,25644,26442,26520,26638,27351,27422,28250,28335,29052,29143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,198,262,347,413,481,539,617,693,762,824", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "134,193,257,342,408,476,534,612,688,757,819,886"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15367,16216,16356,16420,16505,16808,17348,17537,17789,18131,18497,18789", "endColumns": "83,58,63,84,65,67,57,77,75,68,61,66", "endOffsets": "15446,16270,16415,16500,16566,16871,17401,17610,17860,18195,18554,18851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "41,42,43,44,45,46,47,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3745,3843,3950,4047,4146,4250,4354,14195", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3838,3945,4042,4141,4245,4349,4466,14291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,78,81,86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3314,3392,3470,3555,3652,4471,4567,4697,7775,8007,8480,8720,8783,8891,8951,9017,9073,9144,9204,9258,9384,9441,9503,9557,9632,9766,9851,9932,10069,10153,10239,10372,10463,10541,10597,10652,10718,10792,10870,10958,11040,11112,11189,11269,11343,11450,11543,11616,11708,11804,11878,11954,12050,12102,12184,12251,12338,12425,12487,12551,12614,12684,12790,12906,13003,13117,13177,13642", "endLines": "6,36,37,38,39,40,48,49,50,78,81,86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "365,3387,3465,3550,3647,3740,4562,4692,4776,7838,8098,8543,8778,8886,8946,9012,9068,9139,9199,9253,9379,9436,9498,9552,9627,9761,9846,9927,10064,10148,10234,10367,10458,10536,10592,10647,10713,10787,10865,10953,11035,11107,11184,11264,11338,11445,11538,11611,11703,11799,11873,11949,12045,12097,12179,12246,12333,12420,12482,12546,12609,12679,12785,12901,12998,13112,13172,13231,13717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "72,79,147,151,453,457,458", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7235,7843,13416,13722,42817,43253,43340", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "7301,7925,13494,13865,42981,43335,43418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "73,82,83,84", "startColumns": "4,4,4,4", "startOffsets": "7306,8103,8203,8317", "endColumns": "104,99,113,101", "endOffsets": "7406,8198,8312,8414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6060", "endColumns": "131", "endOffsets": "6187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "85,459", "startColumns": "4,4", "startOffsets": "8419,43423", "endColumns": "60,77", "endOffsets": "8475,43496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,340,444,641,688,755,853,927,1149,1234,1342,1411,1465,1529,1819,1892,1957,2011,2064,2141,2259,2370,2427,2509,2588,2673,2739,2845,3113,3196,3273,3336,3397,3459,3520,3591,3673,3779,3876,3963,4066,4158,4234,4313,4398,4596,4794,4902,5031,5093,5733,5795", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,61,54", "endOffsets": "172,335,439,636,683,750,848,922,1144,1229,1337,1406,1460,1524,1814,1887,1952,2006,2059,2136,2254,2365,2422,2504,2583,2668,2734,2840,3108,3191,3268,3331,3392,3454,3515,3586,3668,3774,3871,3958,4061,4153,4229,4308,4393,4591,4789,4897,5026,5088,5728,5790,5845"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,276,282,283,290,300,304,305,306,307,308,314,317,322,324,325,326,327,330,332,333,337,341,342,344,346,354,374,375,376,377,378,395,402,403,404,405,407,408,424", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20289,20411,20574,20774,21775,21822,21889,21987,22061,22283,22368,24289,24543,24733,25308,25992,26065,26525,27427,27710,27787,27905,28016,28073,28633,28881,29321,29469,29575,29843,29926,30172,30351,30412,30729,31300,31371,31541,31753,32800,34706,34809,34901,34977,35056,36579,37271,37469,37577,37706,37835,38475,40118", "endColumns": "121,162,103,196,46,66,97,73,221,84,107,68,53,63,289,72,64,53,52,76,117,110,56,81,78,84,65,105,267,82,76,62,60,61,60,70,81,105,96,86,102,91,75,78,84,197,197,107,128,61,639,61,54", "endOffsets": "20406,20569,20673,20966,21817,21884,21982,22056,22278,22363,22471,24353,24592,24792,25593,26060,26125,26574,27475,27782,27900,28011,28068,28150,28707,28961,29382,29570,29838,29921,29998,30230,30407,30469,30785,31366,31448,31642,31845,32882,34804,34896,34972,35051,35136,36772,37464,37572,37701,37763,38470,38532,40168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,266,336,415,473,532,586,661,745,828,934,1040,1126,1210,1291,1379,1479,1577,1650,1745,1836,1928,2042,2126,2215,2295,2397,2472,2566,2661,2752,2838,2937,3010,3089,3201,3306,3376,3439,4110,4760,4834,4940,5040,5095,5185,5269,5337,5431,5525,5583,5666,5715,5793,5903,5972,6044,6111,6177,6226,6306,6402,6449,6495,6567,6625,6685,6860,7019,7143,7212,7294,7374,7461,7542,7630,7708,7803,7885,7991,8078,8174,8230,8366,8415,8474,8541,8607,8680,8749,8820,8908,8980,9031", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "120,199,261,331,410,468,527,581,656,740,823,929,1035,1121,1205,1286,1374,1474,1572,1645,1740,1831,1923,2037,2121,2210,2290,2392,2467,2561,2656,2747,2833,2932,3005,3084,3196,3301,3371,3434,4105,4755,4829,4935,5035,5090,5180,5264,5332,5426,5520,5578,5661,5710,5788,5898,5967,6039,6106,6172,6221,6301,6397,6444,6490,6562,6620,6680,6855,7014,7138,7207,7289,7369,7456,7537,7625,7703,7798,7880,7986,8073,8169,8225,8361,8410,8469,8536,8602,8675,8744,8815,8903,8975,9026,9106"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,272,273,274,275,279,284,285,286,287,292,293,294,295,296,297,301,302,312,313,315,316,320,321,323,328,335,336,397,398,399,401,406,417,418,419,420,421,422,423,438", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14296,14366,14445,14507,14577,14656,14714,15007,15061,15136,15220,15451,15659,15765,15918,16275,16571,17615,17865,17963,18036,18200,18291,18383,18559,18856,18945,19025,19127,19202,19296,19391,19482,19568,19667,19819,20072,20184,20971,21041,21104,22733,23383,23457,23563,23663,23718,23808,23892,23960,24054,24148,24206,24797,24846,24924,25034,25103,25175,25242,25745,26130,26210,26306,26353,26643,26715,26773,26833,27008,27167,27480,27549,28466,28546,28712,28793,29148,29226,29387,30003,30546,30633,36864,36920,37056,37212,37768,39628,39694,39767,39836,39907,39995,40067,41392", "endColumns": "69,78,61,69,78,57,58,53,74,83,82,105,105,85,83,80,87,99,97,72,94,90,91,113,83,88,79,101,74,93,94,90,85,98,72,78,111,104,69,62,670,649,73,105,99,54,89,83,67,93,93,57,82,48,77,109,68,71,66,65,48,79,95,46,45,71,57,59,174,158,123,68,81,79,86,80,87,77,94,81,105,86,95,55,135,48,58,66,65,72,68,70,87,71,50,79", "endOffsets": "14361,14440,14502,14572,14651,14709,14768,15056,15131,15215,15298,15552,15760,15846,15997,16351,16654,17710,17958,18031,18126,18286,18378,18492,18638,18940,19020,19122,19197,19291,19386,19477,19563,19662,19735,19893,20179,20284,21036,21099,21770,23378,23452,23558,23658,23713,23803,23887,23955,24049,24143,24201,24284,24841,24919,25029,25098,25170,25237,25303,25789,26205,26301,26348,26394,26710,26768,26828,27003,27162,27286,27544,27626,28541,28628,28788,28876,29221,29316,29464,30104,30628,30724,36915,37051,37100,37266,37830,39689,39762,39831,39902,39990,40062,40113,41467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "78", "endOffsets": "129"}, "to": {"startLines": "303", "startColumns": "4", "startOffsets": "27631", "endColumns": "78", "endOffsets": "27705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,211", "endColumns": "74,80,76", "endOffsets": "125,206,283"}, "to": {"startLines": "53,76,80", "startColumns": "4,4,4", "startOffsets": "4973,7604,7930", "endColumns": "74,80,76", "endOffsets": "5043,7680,8002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5048,5154,5311,5441,5551,5708,5838,5953,6192,6342,6449,6606,6734,6881,7024,7092,7154", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "5149,5306,5436,5546,5703,5833,5948,6055,6337,6444,6601,6729,6876,7019,7087,7149,7230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,14037", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,14117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,205,331,394,466,553,615,680,762,859,1015,1281,1567,1645,1717,1799,1893,2013,2151,2219,2293,2458,2527,2584,2657,2738,2862,2989,3105,3191,3267,3349,3420", "endColumns": "74,74,125,62,71,86,61,64,81,96,155,265,285,77,71,81,93,119,137,67,73,164,68,56,72,80,123,126,115,85,75,81,70,162", "endOffsets": "125,200,326,389,461,548,610,675,757,854,1010,1276,1562,1640,1712,1794,1888,2008,2146,2214,2288,2453,2522,2579,2652,2733,2857,2984,3100,3186,3262,3344,3415,3578"}, "to": {"startLines": "164,166,311,329,334,396,409,410,411,412,413,414,415,430,431,432,433,434,435,436,437,440,441,442,443,444,445,446,447,448,449,450,451,452", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14773,14932,28340,30109,30474,36777,38537,38599,38664,38746,38843,38999,39265,40666,40744,40816,40898,40992,41112,41250,41318,41527,41692,41761,41818,41891,41972,42096,42223,42339,42425,42501,42583,42654", "endColumns": "74,74,125,62,71,86,61,64,81,96,155,265,285,77,71,81,93,119,137,67,73,164,68,56,72,80,123,126,115,85,75,81,70,162", "endOffsets": "14843,15002,28461,30167,30541,36859,38594,38659,38741,38838,38994,39260,39546,40739,40811,40893,40987,41107,41245,41313,41387,41687,41756,41813,41886,41967,42091,42218,42334,42420,42496,42578,42649,42812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "34,35", "startColumns": "4,4", "startOffsets": "3081,3192", "endColumns": "110,121", "endOffsets": "3187,3309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "51,52,74,75,77,87,88,145,146,148,149,152,153,155,454,455,456", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4781,4886,7411,7505,7685,8548,8627,13236,13331,13499,13571,13870,13951,14122,42986,43065,43135", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4881,4968,7500,7599,7770,8622,8715,13326,13411,13566,13637,13946,14032,14190,43060,43130,43248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,235,327,410,492,628,724,828,922,1038,1211,1399,1548,1636,1742,1835,1934,2122,2234,2334,2592,2692,2874,2969,3031,3095,3179,3271,3372,3485,3706,3773,3843,3918,4001,4085,4157,4244,4315,4391,4511,4600,4708,4804,4879,4963,5033,5119,5179,5283,5395,5494,5617,5682,5777,5877,5949,6056,6133,6217,6287,6415,6536,6626", "endColumns": "83,95,91,82,81,135,95,103,93,115,172,187,148,87,105,92,98,187,111,99,257,99,181,94,61,63,83,91,100,112,220,66,69,74,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,71,106,76,83,69,127,120,89,54", "endOffsets": "134,230,322,405,487,623,719,823,917,1033,1206,1394,1543,1631,1737,1830,1929,2117,2229,2329,2587,2687,2869,2964,3026,3090,3174,3266,3367,3480,3701,3768,3838,3913,3996,4080,4152,4239,4310,4386,4506,4595,4703,4799,4874,4958,5028,5114,5174,5278,5390,5489,5612,5677,5772,5872,5944,6051,6128,6212,6282,6410,6531,6621,6676"}, "to": {"startLines": "165,236,248,249,250,267,278,280,281,331,338,339,340,343,345,347,348,349,350,351,352,353,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,400,416,425,426,427,428,429,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14848,20678,22476,22568,22651,24597,25649,25794,25898,30235,30790,30963,31151,31453,31647,31850,31943,32042,32230,32342,32442,32700,32887,33069,33164,33226,33290,33374,33466,33567,33680,33901,33968,34038,34113,34196,34280,34352,34439,34510,34586,35141,35230,35338,35434,35509,35593,35663,35749,35809,35913,36025,36124,36247,36312,36407,36507,37105,39551,40173,40257,40327,40455,40576,41472", "endColumns": "83,95,91,82,81,135,95,103,93,115,172,187,148,87,105,92,98,187,111,99,257,99,181,94,61,63,83,91,100,112,220,66,69,74,82,83,71,86,70,75,119,88,107,95,74,83,69,85,59,103,111,98,122,64,94,99,71,106,76,83,69,127,120,89,54", "endOffsets": "14927,20769,22563,22646,22728,24728,25740,25893,25987,30346,30958,31146,31295,31536,31748,31938,32037,32225,32337,32437,32695,32795,33064,33159,33221,33285,33369,33461,33562,33675,33896,33963,34033,34108,34191,34275,34347,34434,34505,34581,34701,35225,35333,35429,35504,35588,35658,35744,35804,35908,36020,36119,36242,36307,36402,36502,36574,37207,39623,40252,40322,40450,40571,40661,41522"}}]}]}