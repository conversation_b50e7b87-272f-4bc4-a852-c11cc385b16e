{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "14267", "endColumns": "87", "endOffsets": "14350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,80", "endOffsets": "258,339"}, "to": {"startLines": "84,163", "startColumns": "4,4", "startOffsets": "8430,14958", "endColumns": "60,84", "endOffsets": "8486,15038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,14008", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,14087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3644,3746,3848,3949,4049,4157,4261,14166", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3741,3843,3944,4044,4152,4256,4375,14262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,213", "endColumns": "77,79,75", "endOffsets": "128,208,284"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4870,7593,7926", "endColumns": "77,79,75", "endOffsets": "4943,7668,7997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5945", "endColumns": "149", "endOffsets": "6090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3223,3300,3380,3463,3557,4380,4475,4602,7769,8002,8491,8734,8801,8910,8973,9040,9099,9173,9236,9290,9405,9463,9525,9579,9654,9783,9873,9962,10103,10185,10267,10406,10492,10576,10636,10687,10753,10826,10904,10990,11071,11143,11220,11295,11366,11467,11561,11640,11736,11830,11904,11980,12066,12119,12206,12272,12357,12448,12510,12574,12637,12706,12808,12909,13005,13106,13170,13616", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "328,3295,3375,3458,3552,3639,4470,4597,4681,7828,8100,8556,8796,8905,8968,9035,9094,9168,9231,9285,9400,9458,9520,9574,9649,9778,9868,9957,10098,10180,10262,10401,10487,10571,10631,10682,10748,10821,10899,10985,11066,11138,11215,11290,11361,11462,11556,11635,11731,11825,11899,11975,12061,12114,12201,12267,12352,12443,12505,12569,12632,12701,12803,12904,13000,13101,13165,13220,13694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,109", "endOffsets": "159,269"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3004,3113", "endColumns": "108,109", "endOffsets": "3108,3218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,273,353,498,667,754", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "175,268,348,493,662,749,828"}, "to": {"startLines": "71,78,146,150,157,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7189,7833,13401,13699,14355,14792,14879", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "7259,7921,13476,13839,14519,14874,14953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7264,8105,8214,8324", "endColumns": "117,108,109,105", "endOffsets": "7377,8209,8319,8425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4686,4785,7382,7486,7673,8561,8644,13225,13318,13481,13549,13844,13925,14092,14524,14607,14675", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "4780,4865,7481,7588,7764,8639,8729,13313,13396,13544,13611,13920,14003,14161,14602,14670,14787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4948,5053,5203,5332,5441,5586,5719,5839,6095,6267,6375,6534,6666,6820,6982,7048,7109", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "5048,5198,5327,5436,5581,5714,5834,5940,6262,6370,6529,6661,6815,6977,7043,7104,7184"}}]}]}