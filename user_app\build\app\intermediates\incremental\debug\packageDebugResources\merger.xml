<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res"><file name="launch_background" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="ic_stat_ic_notification" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-hdpi\ic_stat_ic_notification.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_ic_notification" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-mdpi\ic_stat_ic_notification.png" qualifiers="mdpi-v4" type="drawable"/><file name="launch_background" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_stat_ic_notification" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-xhdpi\ic_stat_ic_notification.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_ic_notification" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-xxhdpi\ic_stat_ic_notification.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_ic_notification" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\drawable-xxxhdpi\ic_stat_ic_notification.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-hdpi\ic_launcher_monochrome.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-mdpi\ic_launcher_monochrome.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xhdpi\ic_launcher_monochrome.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_monochrome.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_monochrome" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_monochrome.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="Theme.MaterialComponents">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="Theme.MaterialComponents">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\res\google-services\debug"/><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\crashlytics\res\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\res\google-services\debug"><file path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">1005114055772-va62uleh7efnru2mmm3a8p9pcatu1jhl.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">1005114055772</string><string name="google_api_key" translatable="false">AIzaSyCBQzsEJ6c0_uitDRticduA-sMYJc6nYKY</string><string name="google_app_id" translatable="false">1:1005114055772:android:6b65c67782a9e5a38a1a99</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCBQzsEJ6c0_uitDRticduA-sMYJc6nYKY</string><string name="google_storage_bucket" translatable="false">g-serveses-user.firebasestorage.app</string><string name="project_id" translatable="false">g-serveses-user</string></file></source><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\crashlytics\res\debug"><file path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\generated\crashlytics\res\debug\values\com_google_firebase_crashlytics_mappingfileid.xml" qualifiers=""><string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>