{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2511,2590,2665,2718,2769,2835,2905,2975,3052,3122,3186,3257,3325,3388,3479,3558,3621,3701,3783,3855,3926,3998,4046,4118,4182,4257,4334,4396,4460,4523,4590,4676,4762,4843,4926,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2506,2585,2660,2713,2764,2830,2900,2970,3047,3117,3181,3252,3320,3383,3474,3553,3616,3696,3778,3850,3921,3993,4041,4113,4177,4252,4329,4391,4455,4518,4585,4671,4757,4838,4921,4978,5033,5106"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3176,3240,3309,3390,4141,4226,4330,7104,7324,7760,7980,8038,8119,8180,8244,8299,8358,8415,8469,8562,8618,8675,8729,8795,8895,8971,9052,9174,9236,9298,9399,9478,9553,9606,9657,9723,9793,9863,9940,10010,10074,10145,10213,10276,10367,10446,10509,10589,10671,10743,10814,10886,10934,11006,11070,11145,11222,11284,11348,11411,11478,11564,11650,11731,11814,11871,12284", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,100,78,74,52,50,65,69,69,76,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72", "endOffsets": "298,3171,3235,3304,3385,3467,4221,4325,4401,7162,7403,7819,8033,8114,8175,8239,8294,8353,8410,8464,8557,8613,8670,8724,8790,8890,8966,9047,9169,9231,9293,9394,9473,9548,9601,9652,9718,9788,9858,9935,10005,10069,10140,10208,10271,10362,10441,10504,10584,10666,10738,10809,10881,10929,11001,11065,11140,11217,11279,11343,11406,11473,11559,11645,11726,11809,11866,11921,12352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "6671,7408,7503,7604", "endColumns": "92,94,100,94", "endOffsets": "6759,7498,7599,7694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,179,266,322,381,452,505,559,623,704,848,1051,1269,1341,1411,1481,1560,1643,1745,1816,1879,2011,2074,2130,2192,2259,2372,2462,2543,2620,2688,2756,2817", "endColumns": "61,61,86,55,58,70,52,53,63,80,143,202,217,71,69,69,78,82,101,70,62,131,62,55,61,66,112,89,80,76,67,67,60,114", "endOffsets": "112,174,261,317,376,447,500,554,618,699,843,1046,1264,1336,1406,1476,1555,1638,1740,1811,1874,2006,2069,2125,2187,2254,2367,2457,2538,2615,2683,2751,2812,2927"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13827,13961,25165,26573,26892,32111,33358,33411,33465,33529,33610,33754,33957,35119,35191,35261,35331,35410,35493,35595,35666,35842,35974,36037,36093,36155,36222,36335,36425,36506,36583,36651,36719,36780", "endColumns": "61,61,86,55,58,70,52,53,63,80,143,202,217,71,69,69,78,82,101,70,62,131,62,55,61,66,112,89,80,76,67,67,60,114", "endOffsets": "13884,14018,25247,26624,26946,32177,33406,33460,33524,33605,33749,33952,34170,35186,35256,35326,35405,35488,35590,35661,35724,35969,36032,36088,36150,36217,36330,36420,36501,36578,36646,36714,36775,36890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,265,338,410,503,570,644,722,811,951,1096,1204,1289,1371,1458,1545,1658,1745,1832,1969,2063,2183,2262,2320,2381,2459,2539,2620,2706,2845,2912,2976,3039,3112,3189,3253,3325,3396,3464,3551,3632,3728,3810,3877,3955,4020,4092,4148,4235,4323,4411,4519,4578,4658,4748,4816,4899,4965,5035,5098,5184,5275,5354", "endColumns": "71,66,70,72,71,92,66,73,77,88,139,144,107,84,81,86,86,112,86,86,136,93,119,78,57,60,77,79,80,85,138,66,63,62,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,67,82,65,69,62,85,90,78,50", "endOffsets": "122,189,260,333,405,498,565,639,717,806,946,1091,1199,1284,1366,1453,1540,1653,1740,1827,1964,2058,2178,2257,2315,2376,2454,2534,2615,2701,2840,2907,2971,3034,3107,3184,3248,3320,3391,3459,3546,3627,3723,3805,3872,3950,4015,4087,4143,4230,4318,4406,4514,4573,4653,4743,4811,4894,4960,5030,5093,5179,5270,5349,5400"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13889,18975,20303,20374,20447,22025,22892,23004,23078,26684,27164,27304,27449,27692,27867,28030,28117,28204,28317,28404,28491,28628,28801,28921,29000,29058,29119,29197,29277,29358,29444,29583,29650,29714,29777,29850,29927,29991,30063,30134,30202,30721,30802,30898,30980,31047,31125,31190,31262,31318,31405,31493,31581,31689,31748,31828,31918,32370,34175,34730,34800,34863,34949,35040,35791", "endColumns": "71,66,70,72,71,92,66,73,77,88,139,144,107,84,81,86,86,112,86,86,136,93,119,78,57,60,77,79,80,85,138,66,63,62,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,67,82,65,69,62,85,90,78,50", "endOffsets": "13956,19037,20369,20442,20514,22113,22954,23073,23151,26768,27299,27444,27552,27772,27944,28112,28199,28312,28399,28486,28623,28717,28916,28995,29053,29114,29192,29272,29353,29439,29578,29645,29709,29772,29845,29922,29986,30058,30129,30197,30284,30797,30893,30975,31042,31120,31185,31257,31313,31400,31488,31576,31684,31743,31823,31913,31981,32448,34236,34795,34858,34944,35035,35114,35837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3564,3664,3758,3854,3947,4040,12783", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3559,3659,3753,3849,3942,4035,4136,12879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4406,4492,6764,6853,7021,7824,7902,11926,12011,12156,12220,12485,12559,12714,37063,37139,37204", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4487,4565,6848,6945,7099,7897,7975,12006,12081,12215,12279,12554,12630,12778,37134,37199,37316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14283,14502,14736,14884,14959,15023,15478,15540,15663,15723,15797,15871,15938,15998,16066,16187,16254,16467,17256,17315,18199,18344,18410,21819,21894,22845,23516,23565,23684,24246,24304,25019,25098,25671,25744", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "14338,14570,14795,14954,15018,15079,15535,15596,15718,15792,15866,15933,15993,16061,16125,16249,16313,16528,17310,17387,18267,18405,18477,21889,21966,22887,23560,23626,23741,24299,24365,25093,25160,25739,25811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,110", "endOffsets": "159,270"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2889,2998", "endColumns": "108,110", "endOffsets": "2993,3104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,325,453,621,701", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "167,250,320,448,616,696,772"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6604,7167,12086,12357,36895,37321,37401", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "6666,7245,12151,12480,37058,37396,37472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4749,4883,5003,5109,5241,5361,5466,5687,5821,5922,6055,6174,6294,6414,6474,6533", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4744,4878,4998,5104,5236,5356,5461,5560,5816,5917,6050,6169,6289,6409,6469,6528,6599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,12635", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,12709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12884,12963,13052,13119,13186,13249,13319", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "12958,13047,13114,13181,13244,13314,13375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4570,6950,7250", "endColumns": "74,70,73", "endOffsets": "4640,7016,7319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14343,15084,15214,15273,15347,15601,16130,16318,16533,16821,17121,17392", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "14411,15135,15268,15342,15400,15658,16182,16380,16594,16879,17176,17450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2852,2914,2975,3433,3864,3934,4025,4114,4169,4253,4331,4392,4467,4547,4602,4676,4724,4795,4881,4947,5013,5080,5146,5191,5259,5343,5386,5429,5498,5556,5615,5726,5837,5929,5992,6062,6126,6204,6272,6339,6402,6477,6541,6621,6697,6776,6825,6919,6964,7019,7081,7140,7208,7267,7330,7408,7468,7515", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2847,2909,2970,3428,3859,3929,4020,4109,4164,4248,4326,4387,4462,4542,4597,4671,4719,4790,4876,4942,5008,5075,5141,5186,5254,5338,5381,5424,5493,5551,5610,5721,5832,5924,5987,6057,6121,6199,6267,6334,6397,6472,6536,6616,6692,6771,6820,6914,6959,7014,7076,7135,7203,7262,7325,7403,7463,7510,7572"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13380,13443,13514,13581,13643,13713,13767,14023,14076,14147,14213,14416,14575,14661,14800,15140,15405,16385,16599,16680,16743,16884,16962,17037,17181,17455,17533,17603,17688,17757,17836,17909,17979,18054,18135,18272,18482,18577,19177,19239,19300,20519,20950,21020,21111,21200,21255,21339,21417,21478,21553,21633,21688,22182,22230,22301,22387,22453,22519,22586,22959,23278,23346,23430,23473,23746,23815,23873,23932,24043,24154,24421,24484,25252,25316,25462,25530,25816,25879,26017,26493,26951,27027,32182,32231,32325,32453,32892,34241,34300,34368,34427,34490,34568,34628,35729", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "13438,13509,13576,13638,13708,13762,13822,14071,14142,14208,14278,14497,14656,14731,14879,15209,15473,16462,16675,16738,16816,16957,17032,17116,17251,17528,17598,17683,17752,17831,17904,17974,18049,18130,18194,18339,18572,18657,19234,19295,19753,20945,21015,21106,21195,21250,21334,21412,21473,21548,21628,21683,21757,22225,22296,22382,22448,22514,22581,22647,22999,23341,23425,23468,23511,23810,23868,23927,24038,24149,24241,24479,24549,25311,25389,25525,25592,25874,25949,26076,26568,27022,27101,32226,32320,32365,32503,32949,34295,34363,34422,34485,34563,34623,34670,35786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "24554", "endColumns": "73", "endOffsets": "24623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02ee5bfe99bcfa22576a7c61152b85ef\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,109,156,256", "endColumns": "53,46,99,81", "endOffsets": "104,151,251,333"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "37546,37600,37647,37747", "endColumns": "53,46,99,81", "endOffsets": "37595,37642,37742,37824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "7699,37477", "endColumns": "60,68", "endOffsets": "7755,37541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,286,368,503,546,606,686,750,911,968,1048,1105,1159,1223,1416,1477,1538,1591,1642,1713,1815,1902,1956,2033,2101,2175,2238,2314,2506,2576,2650,2705,2763,2824,2882,2946,3017,3107,3188,3267,3369,3458,3537,3615,3699,3824,3958,4057,4154,4208,4555,4612", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,56,54", "endOffsets": "137,281,363,498,541,601,681,745,906,963,1043,1100,1154,1218,1411,1472,1533,1586,1637,1708,1810,1897,1951,2028,2096,2170,2233,2309,2501,2571,2645,2700,2758,2819,2877,2941,3012,3102,3183,3262,3364,3453,3532,3610,3694,3819,3953,4052,4149,4203,4550,4607,4662"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18662,18749,18893,19042,19758,19801,19861,19941,20005,20166,20223,21762,21971,22118,22652,23156,23217,23631,24370,24628,24699,24801,24888,24942,25394,25597,25954,26081,26157,26349,26419,26629,26773,26831,27106,27557,27621,27777,27949,28722,30289,30391,30480,30559,30637,31986,32508,32642,32741,32838,32954,33301,34675", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,56,54", "endOffsets": "18744,18888,18970,19172,19796,19856,19936,20000,20161,20218,20298,21814,22020,22177,22840,23212,23273,23679,24416,24694,24796,24883,24937,25014,25457,25666,26012,26152,26344,26414,26488,26679,26826,26887,27159,27616,27687,27862,28025,28796,30386,30475,30554,30632,30716,32106,32637,32736,32833,32887,33296,33353,34725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5565", "endColumns": "121", "endOffsets": "5682"}}]}]}