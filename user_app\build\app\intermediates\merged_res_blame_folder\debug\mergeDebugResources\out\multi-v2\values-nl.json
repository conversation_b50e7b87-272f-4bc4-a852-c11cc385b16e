{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3041,3155", "endColumns": "113,121", "endOffsets": "3150,3272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1070,1159,1238,1301,1394,1456,1522,1580,1653,1717,1773,1895,1952,2014,2070,2146,2280,2365,2451,2589,2670,2749,2873,2963,3040,3097,3148,3214,3292,3375,3463,3539,3614,3693,3766,3837,3946,4040,4118,4207,4297,4371,4452,4539,4592,4671,4738,4819,4903,4965,5029,5092,5163,5271,5383,5485,5596,5657,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "264,350,432,509,607,701,798,920,1001,1065,1154,1233,1296,1389,1451,1517,1575,1648,1712,1768,1890,1947,2009,2065,2141,2275,2360,2446,2584,2665,2744,2868,2958,3035,3092,3143,3209,3287,3370,3458,3534,3609,3688,3761,3832,3941,4035,4113,4202,4292,4366,4447,4534,4587,4666,4733,4814,4898,4960,5024,5087,5158,5266,5378,5480,5591,5652,5707,5788"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3277,3363,3445,3522,3620,4448,4545,4667,7839,8072,8533,8784,8847,8940,9002,9068,9126,9199,9263,9319,9441,9498,9560,9616,9692,9826,9911,9997,10135,10216,10295,10419,10509,10586,10643,10694,10760,10838,10921,11009,11085,11160,11239,11312,11383,11492,11586,11664,11753,11843,11917,11998,12085,12138,12217,12284,12365,12449,12511,12575,12638,12709,12817,12929,13031,13142,13203,13645", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,85,81,76,97,93,96,121,80,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,85,137,80,78,123,89,76,56,50,65,77,82,87,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80", "endOffsets": "314,3358,3440,3517,3615,3709,4540,4662,4743,7898,8156,8607,8842,8935,8997,9063,9121,9194,9258,9314,9436,9493,9555,9611,9687,9821,9906,9992,10130,10211,10290,10414,10504,10581,10638,10689,10755,10833,10916,11004,11080,11155,11234,11307,11378,11487,11581,11659,11748,11838,11912,11993,12080,12133,12212,12279,12360,12444,12506,12570,12633,12704,12812,12924,13026,13137,13198,13253,13721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5005,5113,5264,5392,5503,5670,5797,5920,6169,6347,6453,6622,6748,6911,7093,7161,7224", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "5108,5259,5387,5498,5665,5792,5915,6021,6342,6448,6617,6743,6906,7088,7156,7219,7298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7303,7903,13434,13726,44133,44576,44656", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "7370,7988,13510,13867,44297,44651,44728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14297,14386,14488,14555,14622,14689,14766", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "14381,14483,14550,14617,14684,14761,14838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15925,16719,16862,16924,17005,17299,17840,18029,18280,18596,18946,19240", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "15996,16775,16919,17000,17067,17368,17894,18101,18345,18659,19002,19302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8472,44733", "endColumns": "60,75", "endOffsets": "8528,44804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,212,341,402,468,558,618,680,759,866,1039,1348,1678,1760,1833,1916,2008,2129,2260,2327,2405,2598,2667,2727,2799,2886,3032,3164,3287,3381,3462,3551,3627", "endColumns": "74,81,128,60,65,89,59,61,78,106,172,308,329,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,122,93,80,88,75,157", "endOffsets": "125,207,336,397,463,553,613,675,754,861,1034,1343,1673,1755,1828,1911,2003,2124,2255,2322,2400,2593,2662,2722,2794,2881,3027,3159,3282,3376,3457,3546,3622,3780"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15321,15489,29086,30913,31294,37720,39645,39705,39767,39846,39953,40126,40435,41888,41970,42043,42126,42218,42339,42470,42537,42753,42946,43015,43075,43147,43234,43380,43512,43635,43729,43810,43899,43975", "endColumns": "74,81,128,60,65,89,59,61,78,106,172,308,329,81,72,82,91,120,130,66,77,192,68,59,71,86,145,131,122,93,80,88,75,157", "endOffsets": "15391,15566,29210,30969,31355,37805,39700,39762,39841,39948,40121,40430,40760,41965,42038,42121,42213,42334,42465,42532,42610,42941,43010,43070,43142,43229,43375,43507,43630,43724,43805,43894,43970,44128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,312", "endColumns": "58,46,150,111", "endOffsets": "109,156,307,419"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "44809,44868,44915,45066", "endColumns": "58,46,150,111", "endOffsets": "44863,44910,45061,45173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6026", "endColumns": "142", "endOffsets": "6164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3191,3263,3328,4090,4828,4905,5022,5123,5178,5281,5379,5444,5533,5622,5679,5760,5812,5892,6006,6077,6150,6217,6283,6332,6415,6512,6559,6608,6678,6736,6802,6994,7161,7291,7356,7438,7523,7623,7707,7800,7875,7961,8035,8138,8225,8320,8373,8513,8567,8624,8699,8771,8846,8913,8983,9076,9151,9203", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3186,3258,3323,4085,4823,4900,5017,5118,5173,5276,5374,5439,5528,5617,5674,5755,5807,5887,6001,6072,6145,6212,6278,6327,6410,6507,6554,6603,6673,6731,6797,6989,7156,7286,7351,7433,7518,7618,7702,7795,7870,7956,8030,8133,8220,8315,8368,8508,8562,8619,8694,8766,8841,8908,8978,9071,9146,9198,9276"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14843,14912,14990,15052,15121,15199,15257,15571,15626,15701,15781,16001,16174,16271,16424,16780,17072,18106,18350,18441,18508,18664,18753,18839,19007,19307,19395,19469,19564,19637,19728,19816,19900,19982,20082,20228,20469,20579,21374,21446,21511,23225,23963,24040,24157,24258,24313,24416,24514,24579,24668,24757,24814,25405,25457,25537,25651,25722,25795,25862,26406,26783,26866,26963,27010,27309,27379,27437,27503,27695,27862,28180,28245,29215,29300,29479,29563,29931,30006,30159,30810,31360,31447,37810,37863,38003,38165,38767,40842,40914,40989,41056,41126,41219,41294,42615", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "14907,14985,15047,15116,15194,15252,15316,15621,15696,15776,15857,16093,16266,16351,16498,16857,17155,18199,18436,18503,18591,18748,18834,18941,19084,19390,19464,19559,19632,19723,19811,19895,19977,20077,20146,20298,20574,20676,21441,21506,22268,23958,24035,24152,24253,24308,24411,24509,24574,24663,24752,24809,24890,25452,25532,25646,25717,25790,25857,25923,26450,26861,26958,27005,27054,27374,27432,27498,27690,27857,27987,28240,28322,29295,29395,29558,29651,30001,30087,30228,30908,31442,31537,37858,37998,38052,38217,38837,40909,40984,41051,41121,41214,41289,41341,42688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4748,4840,7478,7575,7754,8612,8688,13258,13345,13515,13580,13872,13953,14119,44302,44386,44456", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4835,4918,7570,7669,7834,8683,8779,13340,13429,13575,13640,13948,14031,14191,44381,44451,44571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "28327", "endColumns": "87", "endOffsets": "28410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15862,16098,16356,16503,16587,16658,17160,17232,17373,17436,17504,17574,17639,17702,17776,17899,17966,18204,19089,19154,20151,20303,20383,24959,25066,26265,27059,27112,27245,27992,28057,28900,29001,29738,29835", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "15920,16169,16419,16582,16653,16714,17227,17294,17431,17499,17569,17634,17697,17771,17835,17961,18024,18275,19149,19235,20223,20378,20464,25061,25144,26311,27107,27187,27304,28052,28122,28996,29081,29830,29926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,325,423,658,704,774,873,945,1213,1273,1362,1426,1481,1545,1882,1953,2019,2072,2125,2217,2350,2469,2526,2610,2689,2771,2838,2934,3260,3337,3415,3483,3543,3607,3667,3740,3830,3928,4031,4117,4215,4315,4389,4468,4555,4774,5006,5124,5254,5319,6058,6122", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,63,54", "endOffsets": "160,320,418,653,699,769,868,940,1208,1268,1357,1421,1476,1540,1877,1948,2014,2067,2120,2212,2345,2464,2521,2605,2684,2766,2833,2929,3255,3332,3410,3478,3538,3602,3662,3735,3825,3923,4026,4112,4210,4310,4384,4463,4550,4769,5001,5119,5249,5314,6053,6117,6172"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20681,20791,20951,21139,22273,22319,22389,22488,22560,22828,22888,24895,25149,25341,25928,26646,26717,27192,28127,28415,28507,28640,28759,28816,29400,29656,30092,30233,30329,30655,30732,30974,31170,31230,31542,32177,32250,32432,32631,33663,35579,35677,35777,35851,35930,37501,38222,38454,38572,38702,38842,39581,41346", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,63,54", "endOffsets": "20786,20946,21044,21369,22314,22384,22483,22555,22823,22883,22972,24954,25199,25400,26260,26712,26778,27240,28175,28502,28635,28754,28811,28895,29474,29733,30154,30324,30650,30727,30805,31037,31225,31289,31597,32245,32335,32525,32729,33744,35672,35772,35846,35925,36012,37715,38449,38567,38697,38762,39576,39640,41396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4923,7674,7993", "endColumns": "81,79,78", "endOffsets": "5000,7749,8067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7375,8161,8262,8373", "endColumns": "102,100,110,98", "endOffsets": "7473,8257,8368,8467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3714,3816,3918,4018,4118,4225,4329,14196", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3811,3913,4013,4113,4220,4324,4443,14292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,238,325,406,486,623,713,814,904,1032,1232,1451,1607,1699,1800,1893,1987,2168,2265,2364,2617,2729,2951,3046,3108,3175,3258,3354,3448,3550,3758,3828,3904,3980,4056,4146,4212,4301,4373,4443,4559,4646,4755,4854,4938,5029,5102,5192,5253,5360,5468,5577,5711,5776,5871,5971,6043,6151,6228,6324,6398,6509,6627,6715", "endColumns": "92,89,86,80,79,136,89,100,89,127,199,218,155,91,100,92,93,180,96,98,252,111,221,94,61,66,82,95,93,101,207,69,75,75,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,71,107,76,95,73,110,117,87,59", "endOffsets": "143,233,320,401,481,618,708,809,899,1027,1227,1446,1602,1694,1795,1888,1982,2163,2260,2359,2612,2724,2946,3041,3103,3170,3253,3349,3443,3545,3753,3823,3899,3975,4051,4141,4207,4296,4368,4438,4554,4641,4750,4849,4933,5024,5097,5187,5248,5355,5463,5572,5706,5771,5866,5966,6038,6146,6223,6319,6393,6504,6622,6710,6770"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15396,21049,22977,23064,23145,25204,26316,26455,26556,31042,31602,31802,32021,32340,32530,32734,32827,32921,33102,33199,33298,33551,33749,33971,34066,34128,34195,34278,34374,34468,34570,34778,34848,34924,35000,35076,35166,35232,35321,35393,35463,36017,36104,36213,36312,36396,36487,36560,36650,36711,36818,36926,37035,37169,37234,37329,37429,38057,40765,41401,41497,41571,41682,41800,42693", "endColumns": "92,89,86,80,79,136,89,100,89,127,199,218,155,91,100,92,93,180,96,98,252,111,221,94,61,66,82,95,93,101,207,69,75,75,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,71,107,76,95,73,110,117,87,59", "endOffsets": "15484,21134,23059,23140,23220,25336,26401,26551,26641,31165,31797,32016,32172,32427,32626,32822,32916,33097,33194,33293,33546,33658,33966,34061,34123,34190,34273,34369,34463,34565,34773,34843,34919,34995,35071,35161,35227,35316,35388,35458,35574,36099,36208,36307,36391,36482,36555,36645,36706,36813,36921,37030,37164,37229,37324,37424,37496,38160,40837,41492,41566,41677,41795,41883,42748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,14036", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,14114"}}]}]}