{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "302", "startColumns": "4", "startOffsets": "26748", "endColumns": "79", "endOffsets": "26823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3302,3370,3436,4010,4563,4638,4744,4838,4893,4980,5064,5130,5214,5297,5354,5428,5477,5560,5658,5727,5798,5865,5931,5976,6051,6145,6195,6241,6311,6369,6434,6601,6754,6870,6935,7013,7083,7170,7247,7327,7396,7490,7560,7668,7748,7834,7884,7994,8042,8099,8172,8234,8302,8368,8440,8523,8587,8636", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3297,3365,3431,4005,4558,4633,4739,4833,4888,4975,5059,5125,5209,5292,5349,5423,5472,5555,5653,5722,5793,5860,5926,5971,6046,6140,6190,6236,6306,6364,6429,6596,6749,6865,6930,7008,7078,7165,7242,7322,7391,7485,7555,7663,7743,7829,7879,7989,8037,8094,8167,8229,8297,8363,8435,8518,8582,8631,8710"}, "to": {"startLines": "156,157,158,159,160,161,162,166,167,168,169,172,174,175,177,182,186,201,204,205,206,208,209,210,212,216,217,218,219,220,221,222,223,224,225,227,230,231,237,238,239,250,251,252,253,254,255,256,257,258,259,260,261,268,269,270,271,272,273,274,278,283,284,285,286,291,292,293,294,295,296,300,301,311,312,314,315,319,320,322,327,334,335,396,397,398,400,405,416,417,418,419,420,421,422,437", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13798,13867,13945,14007,14075,14152,14214,14509,14564,14639,14715,14941,15133,15243,15405,15771,16072,17104,17375,17477,17548,17713,17807,17895,18070,18392,18491,18569,18675,18745,18834,18920,19003,19084,19183,19327,19564,19665,20438,20506,20572,22037,22590,22665,22771,22865,22920,23007,23091,23157,23241,23324,23381,23959,24008,24091,24189,24258,24329,24396,24911,25290,25365,25459,25509,25787,25857,25915,25980,26147,26300,26605,26670,27560,27630,27791,27868,28206,28275,28437,29019,29564,29644,35689,35739,35849,35992,36561,38321,38383,38451,38517,38589,38672,38736,39987", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "13862,13940,14002,14070,14147,14209,14277,14559,14634,14710,14789,15046,15238,15332,15493,15854,16159,17205,17472,17543,17642,17802,17890,18006,18152,18486,18564,18670,18740,18829,18915,18998,19079,19178,19246,19396,19660,19754,20501,20567,21141,22585,22660,22766,22860,22915,23002,23086,23152,23236,23319,23376,23450,24003,24086,24184,24253,24324,24391,24457,24951,25360,25454,25504,25550,25852,25910,25975,26142,26295,26411,26665,26743,27625,27712,27863,27943,28270,28364,28502,29122,29639,29725,35734,35844,35892,36044,36629,38378,38446,38512,38584,38667,38731,38780,40061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2964,3076", "endColumns": "111,113", "endOffsets": "3071,3185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4759,7326,7647", "endColumns": "71,76,71", "endOffsets": "4826,7398,7714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3589,3685,3788,3886,3984,4087,4192,13697", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3680,3783,3881,3979,4082,4187,4299,13793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,199,324,383,450,535,589,646,729,829,990,1231,1487,1561,1631,1701,1792,1909,2034,2105,2176,2353,2425,2487,2560,2635,2757,2867,2967,3053,3130,3213,3278", "endColumns": "68,74,124,58,66,84,53,56,82,99,160,240,255,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,99,85,76,82,64,137", "endOffsets": "119,194,319,378,445,530,584,641,724,824,985,1226,1482,1556,1626,1696,1787,1904,2029,2100,2171,2348,2420,2482,2555,2630,2752,2862,2962,3048,3125,3208,3273,3411"}, "to": {"startLines": "163,165,310,328,333,395,408,409,410,411,412,413,414,429,430,431,432,433,434,435,436,439,440,441,442,443,444,445,446,447,448,449,450,451", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14282,14434,27435,29127,29497,35604,37292,37346,37403,37486,37586,37747,37988,39298,39372,39442,39512,39603,39720,39845,39916,40123,40300,40372,40434,40507,40582,40704,40814,40914,41000,41077,41160,41225", "endColumns": "68,74,124,58,66,84,53,56,82,99,160,240,255,73,69,69,90,116,124,70,70,176,71,61,72,74,121,109,99,85,76,82,64,137", "endOffsets": "14346,14504,27555,29181,29559,35684,37341,37398,37481,37581,37742,37983,38239,39367,39437,39507,39598,39715,39840,39911,39982,40295,40367,40429,40502,40577,40699,40809,40909,40995,41072,41155,41220,41358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,453,454,455", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4682,7128,7225,7403,8248,8333,12771,12857,13020,13085,13367,13453,13624,41531,41609,41676", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4677,4754,7220,7321,7486,8328,8413,12852,12935,13080,13146,13448,13537,13692,41604,41671,41794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4831,4937,5084,5207,5314,5450,5574,5693,5930,6074,6179,6326,6448,6588,6739,6803,6871", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4932,5079,5202,5309,5445,5569,5688,5796,6069,6174,6321,6443,6583,6734,6798,6866,6950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,229,324,404,483,605,696,799,892,1021,1206,1399,1548,1636,1732,1825,1926,2072,2176,2273,2506,2621,2802,2893,2951,3014,3097,3189,3282,3386,3568,3635,3701,3776,3854,3940,4003,4077,4151,4220,4330,4421,4528,4619,4689,4768,4836,4921,4981,5081,5187,5290,5413,5478,5569,5665,5734,5829,5906,5990,6063,6168,6278,6364", "endColumns": "82,90,94,79,78,121,90,102,92,128,184,192,148,87,95,92,100,145,103,96,232,114,180,90,57,62,82,91,92,103,181,66,65,74,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,68,94,76,83,72,104,109,85,56", "endOffsets": "133,224,319,399,478,600,691,794,887,1016,1201,1394,1543,1631,1727,1820,1921,2067,2171,2268,2501,2616,2797,2888,2946,3009,3092,3184,3277,3381,3563,3630,3696,3771,3849,3935,3998,4072,4146,4215,4325,4416,4523,4614,4684,4763,4831,4916,4976,5076,5182,5285,5408,5473,5564,5660,5729,5824,5901,5985,6058,6163,6273,6359,6416"}, "to": {"startLines": "164,235,247,248,249,266,277,279,280,330,337,338,339,342,344,346,347,348,349,350,351,352,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,399,415,424,425,426,427,428,438", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14351,20132,21783,21878,21958,23773,24820,24956,25059,29245,29792,29977,30170,30461,30644,30839,30932,31033,31179,31283,31380,31613,31811,31992,32083,32141,32204,32287,32379,32472,32576,32758,32825,32891,32966,33044,33130,33193,33267,33341,33410,33997,34088,34195,34286,34356,34435,34503,34588,34648,34748,34854,34957,35080,35145,35236,35332,35897,38244,38840,38924,38997,39102,39212,40066", "endColumns": "82,90,94,79,78,121,90,102,92,128,184,192,148,87,95,92,100,145,103,96,232,114,180,90,57,62,82,91,92,103,181,66,65,74,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,68,94,76,83,72,104,109,85,56", "endOffsets": "14429,20218,21873,21953,22032,23890,24906,25054,25147,29369,29972,30165,30314,30544,30735,30927,31028,31174,31278,31375,31608,31723,31987,32078,32136,32199,32282,32374,32467,32571,32753,32820,32886,32961,33039,33125,33188,33262,33336,33405,33515,34083,34190,34281,34351,34430,34498,34583,34643,34743,34849,34952,35075,35140,35231,35327,35396,35987,38316,38919,38992,39097,39207,39293,40118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "170,173,176,178,179,180,187,188,190,191,192,193,194,195,196,198,199,202,213,214,226,228,229,263,264,276,287,288,290,297,298,308,309,317,318", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14794,15051,15337,15498,15583,15650,16164,16229,16363,16428,16501,16573,16642,16703,16772,16895,16962,17210,18157,18222,19251,19401,19479,23532,23648,24770,25555,25602,25723,26416,26475,27261,27350,28032,28121", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "14854,15128,15400,15578,15645,15706,16224,16286,16423,16496,16568,16637,16698,16767,16831,16957,17021,17296,18217,18316,19322,19474,19559,23643,23713,24815,25597,25665,25782,26470,26547,27345,27430,28116,28201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,326,428,643,692,760,849,920,1132,1192,1280,1357,1412,1476,1784,1852,1922,1975,2028,2105,2220,2316,2384,2461,2535,2619,2687,2783,3047,3121,3199,3258,3320,3381,3443,3510,3585,3680,3779,3862,3981,4084,4157,4236,4339,4542,4754,4871,5000,5054,5650,5712", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,61,54", "endOffsets": "161,321,423,638,687,755,844,915,1127,1187,1275,1352,1407,1471,1779,1847,1917,1970,2023,2100,2215,2311,2379,2456,2530,2614,2682,2778,3042,3116,3194,3253,3315,3376,3438,3505,3580,3675,3774,3857,3976,4079,4152,4231,4334,4537,4749,4866,4995,5049,5645,5707,5762"}, "to": {"startLines": "232,233,234,236,240,241,242,243,244,245,246,262,265,267,275,281,282,289,299,303,304,305,306,307,313,316,321,323,324,325,326,329,331,332,336,340,341,343,345,353,373,374,375,376,377,394,401,402,403,404,406,407,423", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19759,19870,20030,20223,21146,21195,21263,21352,21423,21635,21695,23455,23718,23895,24462,25152,25220,25670,26552,26828,26905,27020,27116,27184,27717,27948,28369,28507,28603,28867,28941,29186,29374,29436,29730,30319,30386,30549,30740,31728,33520,33639,33742,33815,33894,35401,36049,36261,36378,36507,36634,37230,38785", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,61,54", "endOffsets": "19865,20025,20127,20433,21190,21258,21347,21418,21630,21690,21778,23527,23768,23954,24765,25215,25285,25718,26600,26900,27015,27111,27179,27256,27786,28027,28432,28598,28862,28936,29014,29240,29431,29492,29787,30381,30456,30639,30834,31806,33634,33737,33810,33889,33992,35599,36256,36373,36502,36556,37225,37287,38835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02ee5bfe99bcfa22576a7c61152b85ef\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,163,281", "endColumns": "58,48,117,107", "endOffsets": "109,158,276,384"}, "to": {"startLines": "459,460,461,462", "startColumns": "4,4,4,4", "startOffsets": "42032,42091,42140,42258", "endColumns": "58,48,117,107", "endOffsets": "42086,42135,42253,42361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2785,2877,2955,3009,3062,3128,3198,3276,3362,3442,3514,3592,3661,3730,3828,3910,3998,4091,4185,4259,4328,4423,4475,4558,4626,4711,4799,4861,4925,4988,5058,5158,5254,5351,5444,5502,5559", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2780,2872,2950,3004,3057,3123,3193,3271,3357,3437,3509,3587,3656,3725,3823,3905,3993,4086,4180,4254,4323,4418,4470,4553,4621,4706,4794,4856,4920,4983,5053,5153,5249,5346,5439,5497,5554,5631"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3190,3263,3335,3418,3503,4304,4403,4516,7491,7719,8178,8418,8478,8565,8631,8696,8757,8821,8882,8936,9037,9098,9158,9212,9282,9393,9480,9561,9704,9783,9865,9997,10089,10167,10221,10274,10340,10410,10488,10574,10654,10726,10804,10873,10942,11040,11122,11210,11303,11397,11471,11540,11635,11687,11770,11838,11923,12011,12073,12137,12200,12270,12370,12466,12563,12656,12714,13151", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "330,3258,3330,3413,3498,3584,4398,4511,4591,7556,7804,8243,8473,8560,8626,8691,8752,8816,8877,8931,9032,9093,9153,9207,9277,9388,9475,9556,9699,9778,9860,9992,10084,10162,10216,10269,10335,10405,10483,10569,10649,10721,10799,10868,10937,11035,11117,11205,11298,11392,11466,11535,11630,11682,11765,11833,11918,12006,12068,12132,12195,12265,12365,12461,12558,12651,12709,12766,13223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,341,480,648,728", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "170,256,336,475,643,723,801"}, "to": {"startLines": "71,78,146,150,452,456,457", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6955,7561,12940,13228,41363,41799,41879", "endColumns": "69,85,79,138,167,79,77", "endOffsets": "7020,7642,13015,13362,41526,41874,41952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7025,7809,7908,8019", "endColumns": "102,98,110,97", "endOffsets": "7123,7903,8014,8112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5801", "endColumns": "128", "endOffsets": "5925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13542", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "171,181,183,184,185,189,197,200,203,207,211,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14859,15711,15859,15923,16008,16291,16836,17026,17301,17647,18011,18321", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "14936,15766,15918,16003,16067,16358,16890,17099,17370,17708,18065,18387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "84,458", "startColumns": "4,4", "startOffsets": "8117,41957", "endColumns": "60,74", "endOffsets": "8173,42027"}}]}]}