{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4491,6717,7007", "endColumns": "71,68,70", "endOffsets": "4558,6781,7073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,244", "endColumns": "48,46,92,69", "endOffsets": "99,146,239,309"}, "to": {"startLines": "465,466,467,468", "startColumns": "4,4,4,4", "startOffsets": "35317,35366,35413,35506", "endColumns": "48,46,92,69", "endOffsets": "35361,35408,35501,35571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,175,254,305,364,427,477,528,586,655,773,938,1121,1189,1254,1317,1392,1470,1554,1619,1682,1795,1860,1913,1975,2041,2126,2212,2290,2361,2428,2491,2551", "endColumns": "58,60,78,50,58,62,49,50,57,68,117,164,182,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,77,70,66,62,59,93", "endOffsets": "109,170,249,300,359,422,472,523,581,650,768,933,1116,1184,1249,1312,1387,1465,1549,1614,1677,1790,1855,1908,1970,2036,2121,2207,2285,2356,2423,2486,2546,2640"}, "to": {"startLines": "170,172,316,334,339,401,414,415,416,417,418,419,420,435,436,437,438,439,440,441,442,445,446,447,448,449,450,451,452,453,454,455,456,457", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13432,13557,23884,25185,25478,30393,31446,31496,31547,31605,31674,31792,31957,33030,33098,33163,33226,33301,33379,33463,33528,33703,33816,33881,33934,33996,34062,34147,34233,34311,34382,34449,34512,34572", "endColumns": "58,60,78,50,58,62,49,50,57,68,117,164,182,67,64,62,74,77,83,64,62,112,64,52,61,65,84,85,77,70,66,62,59,93", "endOffsets": "13486,13613,23958,25231,25532,30451,31491,31542,31600,31669,31787,31952,32135,33093,33158,33221,33296,33374,33458,33523,33586,33811,33876,33929,33991,34057,34142,34228,34306,34377,34444,34507,34567,34661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,190,261,334,407,485,554,625,697,769,887,1009,1104,1189,1271,1355,1441,1545,1633,1719,1838,1923,2025,2104,2161,2220,2295,2373,2451,2536,2657,2720,2782,2843,2913,2986,3045,3114,3182,3245,3333,3414,3505,3586,3651,3722,3786,3857,3913,3996,4072,4156,4255,4313,4390,4474,4541,4621,4681,4746,4805,4886,4971,5044", "endColumns": "65,68,70,72,72,77,68,70,71,71,117,121,94,84,81,83,85,103,87,85,118,84,101,78,56,58,74,77,77,84,120,62,61,60,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,66,79,59,64,58,80,84,72,49", "endOffsets": "116,185,256,329,402,480,549,620,692,764,882,1004,1099,1184,1266,1350,1436,1540,1628,1714,1833,1918,2020,2099,2156,2215,2290,2368,2446,2531,2652,2715,2777,2838,2908,2981,3040,3109,3177,3240,3328,3409,3500,3581,3646,3717,3781,3852,3908,3991,4067,4151,4250,4308,4385,4469,4536,4616,4676,4741,4800,4881,4966,5039,5089"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,336,343,344,345,348,350,352,353,354,355,356,357,358,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,405,421,430,431,432,433,434,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13491,18409,19527,19598,19671,21064,21848,21961,22032,25290,25741,25859,25981,26203,26378,26537,26621,26707,26811,26899,26985,27104,27266,27368,27447,27504,27563,27638,27716,27794,27879,28000,28063,28125,28186,28256,28329,28388,28457,28525,28588,29084,29165,29256,29337,29402,29473,29537,29608,29664,29747,29823,29907,30006,30064,30141,30225,30623,32140,32667,32732,32791,32872,32957,33653", "endColumns": "65,68,70,72,72,77,68,70,71,71,117,121,94,84,81,83,85,103,87,85,118,84,101,78,56,58,74,77,77,84,120,62,61,60,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,66,79,59,64,58,80,84,72,49", "endOffsets": "13552,18473,19593,19666,19739,21137,21912,22027,22099,25357,25854,25976,26071,26283,26455,26616,26702,26806,26894,26980,27099,27184,27363,27442,27499,27558,27633,27711,27789,27874,27995,28058,28120,28181,28251,28324,28383,28452,28520,28583,28671,29160,29251,29332,29397,29468,29532,29603,29659,29742,29818,29902,30001,30059,30136,30220,30287,30698,32195,32727,32786,32867,32952,33025,33698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,12278", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,12352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "84,464", "startColumns": "4,4", "startOffsets": "7442,35245", "endColumns": "60,71", "endOffsets": "7498,35312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "71,78,146,150,458,462,463", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6389,6926,11746,12011,34666,35090,35169", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "6450,7002,11811,12125,34829,35164,35240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "6455,7156,7248,7349", "endColumns": "82,91,100,92", "endOffsets": "6533,7243,7344,7437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13925,14640,14767,14827,14900,15153,15654,15834,16048,16332,16618,16874", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "13991,14692,14822,14895,14953,15210,15706,15896,16109,16387,16670,16932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,916,994,1053,1111,1189,1250,1307,1363,1422,1480,1534,1620,1676,1734,1788,1853,1946,2020,2098,2218,2281,2344,2443,2520,2594,2644,2695,2761,2825,2893,2968,3040,3101,3172,3239,3299,3387,3467,3530,3613,3698,3772,3837,3913,3961,4035,4099,4175,4253,4315,4379,4442,4508,4588,4668,4744,4825,4879,4934", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "242,305,366,433,502,579,669,776,849,911,989,1048,1106,1184,1245,1302,1358,1417,1475,1529,1615,1671,1729,1783,1848,1941,2015,2093,2213,2276,2339,2438,2515,2589,2639,2690,2756,2820,2888,2963,3035,3096,3167,3234,3294,3382,3462,3525,3608,3693,3767,3832,3908,3956,4030,4094,4170,4248,4310,4374,4437,4503,4583,4663,4739,4820,4874,4929,4998"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3073,3136,3197,3264,3333,4071,4161,4268,6864,7078,7503,7713,7771,7849,7910,7967,8023,8082,8140,8194,8280,8336,8394,8448,8513,8606,8680,8758,8878,8941,9004,9103,9180,9254,9304,9355,9421,9485,9553,9628,9700,9761,9832,9899,9959,10047,10127,10190,10273,10358,10432,10497,10573,10621,10695,10759,10835,10913,10975,11039,11102,11168,11248,11328,11404,11485,11539,11942", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,62,60,66,68,76,89,106,72,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,77,119,62,62,98,76,73,49,50,65,63,67,74,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68", "endOffsets": "292,3131,3192,3259,3328,3405,4156,4263,4336,6921,7151,7557,7766,7844,7905,7962,8018,8077,8135,8189,8275,8331,8389,8443,8508,8601,8675,8753,8873,8936,8999,9098,9175,9249,9299,9350,9416,9480,9548,9623,9695,9756,9827,9894,9954,10042,10122,10185,10268,10353,10427,10492,10568,10616,10690,10754,10830,10908,10970,11034,11097,11163,11243,11323,11399,11480,11534,11589,12006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3410,3502,3601,3695,3789,3882,3975,12424", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3497,3596,3690,3784,3877,3970,4066,12520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4664,4792,4907,5009,5116,5232,5334,5535,5645,5746,5875,5990,6097,6205,6260,6317", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "4659,4787,4902,5004,5111,5227,5329,5422,5640,5741,5870,5985,6092,6200,6255,6312,6384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4950,4993,5048,5106,5164,5255,5347,5424,5484,5547,5606,5681,5744,5804,5866,5937,5995,6069,6138,6215,6264,6337,6382,6432,6488,6544,6605,6664,6725,6796,6855,6900", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4945,4988,5043,5101,5159,5250,5342,5419,5479,5542,5601,5676,5739,5799,5861,5932,5990,6064,6133,6210,6259,6332,6377,6427,6483,6539,6600,6659,6720,6791,6850,6895,6957"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,317,318,320,321,325,326,328,333,340,341,402,403,404,406,411,422,423,424,425,426,427,428,443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12999,13059,13127,13188,13249,13318,13372,13618,13666,13731,13797,13996,14151,14235,14372,14697,14958,15901,16114,16193,16256,16392,16465,16535,16675,16937,17013,17083,17166,17231,17306,17379,17448,17517,17595,17728,17931,18023,18590,18653,18714,19744,20061,20126,20210,20290,20345,20421,20493,20551,20622,20695,20750,21206,21251,21321,21403,21460,21525,21592,21917,22224,22288,22365,22408,22672,22727,22785,22843,22934,23026,23269,23329,23963,24022,24164,24227,24493,24555,24686,25111,25537,25606,30456,30505,30578,30703,31097,32200,32256,32317,32376,32437,32508,32567,33591", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "13054,13122,13183,13244,13313,13367,13427,13661,13726,13792,13860,14075,14230,14303,14436,14762,15024,15976,16188,16251,16327,16460,16530,16613,16740,17008,17078,17161,17226,17301,17374,17443,17512,17590,17650,17790,18018,18103,18648,18709,19036,20056,20121,20205,20285,20340,20416,20488,20546,20617,20690,20745,20815,21246,21316,21398,21455,21520,21587,21654,21956,22283,22360,22403,22446,22722,22780,22838,22929,23021,23098,23324,23387,24017,24092,24222,24282,24550,24621,24739,25180,25601,25678,30500,30573,30618,30748,31148,32251,32312,32371,32432,32503,32562,32607,33648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,459,460,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4341,4417,6538,6626,6786,7562,7636,11594,11672,11816,11879,12130,12203,12357,34834,34909,34974", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4412,4486,6621,6712,6859,7631,7708,11667,11741,11874,11937,12198,12273,12419,34904,34969,35085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5427", "endColumns": "107", "endOffsets": "5530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12525,12600,12684,12749,12815,12875,12937", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "12595,12679,12744,12810,12870,12932,12994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1271,1331,1391,1442,1493,1559,1643,1720,1775,1846,1913,1980,2040,2115,2268,2335,2407,2461,2519,2577,2635,2696,2762,2852,2929,3006,3096,3180,3250,3329,3414,3515,3624,3716,3810,3859,4095,4152", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,66,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1266,1326,1386,1437,1488,1554,1638,1715,1770,1841,1908,1975,2035,2110,2263,2330,2402,2456,2514,2572,2630,2691,2757,2847,2924,3001,3091,3175,3245,3324,3409,3510,3619,3711,3805,3854,4090,4147,4202"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,309,310,311,312,313,319,322,327,329,330,331,332,335,337,338,342,346,347,349,351,359,379,380,381,382,383,400,407,408,409,410,412,413,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18108,18197,18332,18478,19041,19085,19143,19218,19282,19400,19455,20820,21011,21142,21659,22104,22164,22559,23218,23392,23458,23542,23619,23674,24097,24287,24626,24744,24819,24972,25039,25236,25362,25420,25683,26076,26137,26288,26460,27189,28676,28766,28850,28920,28999,30292,30753,30862,30954,31048,31153,31389,32612", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,66,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,56,54", "endOffsets": "18192,18327,18404,18585,19080,19138,19213,19277,19395,19450,19522,20871,21059,21201,21798,22159,22219,22605,23264,23453,23537,23614,23669,23740,24159,24349,24681,24814,24967,25034,25106,25285,25415,25473,25736,26132,26198,26373,26532,27261,28761,28845,28915,28994,29079,30388,30857,30949,31043,31092,31384,31441,32662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,108", "endOffsets": "156,265"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2858,2964", "endColumns": "105,108", "endOffsets": "2959,3068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,314,315,323,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13865,14080,14308,14441,14514,14579,15029,15092,15215,15275,15343,15407,15468,15526,15592,15711,15776,15981,16745,16804,17655,17795,17860,20876,20948,21803,22451,22497,22610,23103,23156,23745,23817,24354,24424", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "13920,14146,14367,14509,14574,14635,15087,15148,15270,15338,15402,15463,15521,15587,15649,15771,15829,16043,16799,16869,17723,17855,17926,20943,21006,21843,22492,22554,22667,23151,23213,23812,23879,24419,24488"}}]}]}