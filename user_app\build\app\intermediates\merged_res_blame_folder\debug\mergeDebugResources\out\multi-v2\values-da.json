{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7120,7718,13096,13385,42661,43095,43175", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "7185,7801,13170,13528,42825,43170,43247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4671,4763,7302,7397,7572,8404,8481,12925,13014,13175,13240,13533,13614,13778,42830,42908,42975", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4758,4838,7392,7491,7649,8476,8565,13009,13091,13235,13300,13609,13693,13843,42903,42970,43090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "27565", "endColumns": "81", "endOffsets": "27642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13949,14038,14142,14209,14276,14342,14415", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "14033,14137,14204,14271,14337,14410,14477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13698", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15533,16333,16467,16529,16614,16893,17418,17610,17849,18162,18502,18795", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "15606,16385,16524,16609,16669,16958,17472,17678,17916,18223,18557,18857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2836,2922,3000,3056,3107,3173,3241,3315,3404,3479,3551,3629,3699,3772,3876,3960,4037,4125,4214,4288,4361,4446,4495,4573,4639,4719,4802,4864,4928,4991,5060,5168,5271,5372,5471,5531,5586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2831,2917,2995,3051,3102,3168,3236,3310,3399,3474,3546,3624,3694,3767,3871,3955,4032,4120,4209,4283,4356,4441,4490,4568,4634,4714,4797,4859,4923,4986,5055,5163,5266,5367,5466,5526,5581,5661"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3208,3286,3362,3440,3537,4344,4444,4593,7654,7881,8331,8570,8630,8717,8781,8843,8905,8973,9038,9094,9212,9270,9331,9387,9462,9588,9674,9754,9895,9973,10053,10175,10261,10339,10395,10446,10512,10580,10654,10743,10818,10890,10968,11038,11111,11215,11299,11376,11464,11553,11627,11700,11785,11834,11912,11978,12058,12141,12203,12267,12330,12399,12507,12610,12711,12810,12870,13305", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,121,85,77,55,50,65,67,73,88,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79", "endOffsets": "317,3281,3357,3435,3532,3612,4439,4588,4666,7713,7962,8399,8625,8712,8776,8838,8900,8968,9033,9089,9207,9265,9326,9382,9457,9583,9669,9749,9890,9968,10048,10170,10256,10334,10390,10441,10507,10575,10649,10738,10813,10885,10963,11033,11106,11210,11294,11371,11459,11548,11622,11695,11780,11829,11907,11973,12053,12136,12198,12262,12325,12394,12502,12605,12706,12805,12865,12920,13380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,234,321,399,476,605,697,795,884,1003,1181,1372,1510,1599,1705,1796,1897,2078,2192,2290,2539,2647,2835,2929,2987,3051,3132,3227,3327,3437,3650,3719,3785,3859,3936,4025,4091,4172,4243,4311,4419,4506,4616,4706,4777,4865,4932,5018,5075,5175,5281,5384,5510,5569,5660,5760,5831,5929,6000,6090,6161,6266,6381,6472", "endColumns": "86,91,86,77,76,128,91,97,88,118,177,190,137,88,105,90,100,180,113,97,248,107,187,93,57,63,80,94,99,109,212,68,65,73,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,70,97,70,89,70,104,114,90,53", "endOffsets": "137,229,316,394,471,600,692,790,879,998,1176,1367,1505,1594,1700,1791,1892,2073,2187,2285,2534,2642,2830,2924,2982,3046,3127,3222,3322,3432,3645,3714,3780,3854,3931,4020,4086,4167,4238,4306,4414,4501,4611,4701,4772,4860,4927,5013,5070,5170,5276,5379,5505,5564,5655,5755,5826,5924,5995,6085,6156,6261,6376,6467,6521"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15018,20611,22412,22499,22577,24544,25601,25738,25836,30208,30741,30919,31110,31394,31583,31785,31876,31977,32158,32272,32370,32619,32819,33007,33101,33159,33223,33304,33399,33499,33609,33822,33891,33957,34031,34108,34197,34263,34344,34415,34483,35024,35111,35221,35311,35382,35470,35537,35623,35680,35780,35886,35989,36115,36174,36265,36365,36953,39457,40064,40154,40225,40330,40445,41314", "endColumns": "86,91,86,77,76,128,91,97,88,118,177,190,137,88,105,90,100,180,113,97,248,107,187,93,57,63,80,94,99,109,212,68,65,73,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,70,97,70,89,70,104,114,90,53", "endOffsets": "15100,20698,22494,22572,22649,24668,25688,25831,25920,30322,30914,31105,31243,31478,31684,31871,31972,32153,32267,32365,32614,32722,33002,33096,33154,33218,33299,33394,33494,33604,33817,33886,33952,34026,34103,34192,34258,34339,34410,34478,34586,35106,35216,35306,35377,35465,35532,35618,35675,35775,35881,35984,36110,36169,36260,36360,36431,37046,39523,40149,40220,40325,40440,40531,41363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3617,3713,3815,3912,4010,4117,4226,13848", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3708,3810,3907,4005,4112,4221,4339,13944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4843,7496,7806", "endColumns": "74,75,74", "endOffsets": "4913,7567,7876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7190,7967,8066,8173", "endColumns": "111,98,106,96", "endOffsets": "7297,8061,8168,8265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,204,321,379,443,530,584,640,714,816,985,1250,1540,1614,1683,1767,1855,1971,2105,2172,2242,2412,2481,2540,2614,2695,2831,2955,3068,3151,3226,3311,3378", "endColumns": "68,79,116,57,63,86,53,55,73,101,168,264,289,73,68,83,87,115,133,66,69,169,68,58,73,80,135,123,112,82,74,84,66,156", "endOffsets": "119,199,316,374,438,525,579,635,709,811,980,1245,1535,1609,1678,1762,1850,1966,2100,2167,2237,2407,2476,2535,2609,2690,2826,2950,3063,3146,3221,3306,3373,3530"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14949,15105,28311,30083,30449,36634,38447,38501,38557,38631,38733,38902,39167,40536,40610,40679,40763,40851,40967,41101,41168,41368,41538,41607,41666,41740,41821,41957,42081,42194,42277,42352,42437,42504", "endColumns": "68,79,116,57,63,86,53,55,73,101,168,264,289,73,68,83,87,115,133,66,69,169,68,58,73,80,135,123,112,82,74,84,66,156", "endOffsets": "15013,15180,28423,30136,30508,36716,38496,38552,38626,38728,38897,39162,39452,40605,40674,40758,40846,40962,41096,41163,41233,41533,41602,41661,41735,41816,41952,42076,42189,42272,42347,42432,42499,42656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2974,3089", "endColumns": "114,118", "endOffsets": "3084,3203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8270,43252", "endColumns": "60,73", "endOffsets": "8326,43321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,324,419,628,676,743,842,911,1166,1226,1318,1389,1444,1508,1826,1900,1965,2018,2071,2167,2291,2407,2464,2551,2630,2713,2779,2878,3176,3253,3330,3397,3457,3519,3579,3648,3725,3825,3921,4013,4117,4209,4282,4361,4446,4644,4851,4963,5083,5138,5855,5919", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,63,54", "endOffsets": "162,319,414,623,671,738,837,906,1161,1221,1313,1384,1439,1503,1821,1895,1960,2013,2066,2162,2286,2402,2459,2546,2625,2708,2774,2873,3171,3248,3325,3392,3452,3514,3574,3643,3720,3820,3916,4008,4112,4204,4277,4356,4441,4639,4846,4958,5078,5133,5850,5914,5969"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20247,20359,20516,20703,21722,21770,21837,21936,22005,22260,22320,24224,24489,24673,25235,25925,25999,26459,27364,27647,27743,27867,27983,28040,28606,28858,29284,29430,29529,29827,29904,30141,30327,30387,30681,31248,31317,31483,31689,32727,34591,34695,34787,34860,34939,36436,37105,37312,37424,37544,37666,38383,40009", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,63,54", "endOffsets": "20354,20511,20606,20907,21765,21832,21931,22000,22255,22315,22407,24290,24539,24732,25548,25994,26059,26507,27412,27738,27862,27978,28035,28122,28680,28936,29345,29524,29822,29899,29976,30203,30382,30444,30736,31312,31389,31578,31780,32814,34690,34782,34855,34934,35019,36629,37307,37419,37539,37594,38378,38442,40059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5913", "endColumns": "126", "endOffsets": "6035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4918,5024,5184,5311,5420,5563,5688,5808,6040,6196,6302,6464,6591,6736,6914,6980,7042", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "5019,5179,5306,5415,5558,5683,5803,5908,6191,6297,6459,6586,6731,6909,6975,7037,7115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15468,15709,15971,16117,16205,16272,16757,16826,16963,17027,17095,17163,17223,17281,17354,17477,17547,17776,18642,18706,19729,19878,19955,24295,24404,25553,26329,26384,26512,27226,27295,28127,28224,28941,29029", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "15528,15782,16033,16200,16267,16328,16821,16888,17022,17090,17158,17218,17276,17349,17413,17542,17605,17844,18701,18790,19798,19950,20041,24399,24484,25596,26379,26454,26569,27290,27359,28219,28306,29024,29111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3157,3227,3292,3967,4618,4692,4809,4909,4964,5062,5152,5220,5311,5397,5454,5537,5588,5665,5761,5831,5902,5969,6035,6080,6158,6250,6297,6345,6413,6471,6536,6718,6881,6997,7061,7145,7222,7323,7412,7496,7572,7664,7744,7846,7928,8014,8067,8198,8246,8300,8367,8434,8508,8572,8644,8732,8798,8848", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3152,3222,3287,3962,4613,4687,4804,4904,4959,5057,5147,5215,5306,5392,5449,5532,5583,5660,5756,5826,5897,5964,6030,6075,6153,6245,6292,6340,6408,6466,6531,6713,6876,6992,7056,7140,7217,7318,7407,7491,7567,7659,7739,7841,7923,8009,8062,8193,8241,8295,8362,8429,8503,8567,8639,8727,8793,8843,8919"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14482,14550,14627,14687,14755,14830,14890,15185,15238,15310,15390,15611,15787,15885,16038,16390,16674,17683,17921,18010,18076,18228,18314,18398,18562,18862,18949,19031,19133,19207,19295,19391,19477,19558,19657,19803,20046,20150,20912,20982,21047,22654,23305,23379,23496,23596,23651,23749,23839,23907,23998,24084,24141,24737,24788,24865,24961,25031,25102,25169,25693,26064,26142,26234,26281,26574,26642,26700,26765,26947,27110,27417,27481,28428,28505,28685,28774,29116,29192,29350,29981,30513,30595,36721,36774,36905,37051,37599,39528,39595,39669,39733,39805,39893,39959,41238", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "14545,14622,14682,14750,14825,14885,14944,15233,15305,15385,15463,15704,15880,15966,16112,16462,16752,17771,18005,18071,18157,18309,18393,18497,18637,18944,19026,19128,19202,19290,19386,19472,19553,19652,19724,19873,20145,20242,20977,21042,21717,23300,23374,23491,23591,23646,23744,23834,23902,23993,24079,24136,24219,24783,24860,24956,25026,25097,25164,25230,25733,26137,26229,26276,26324,26637,26695,26760,26942,27105,27221,27476,27560,28500,28601,28769,28853,29187,29279,29425,30078,30590,30676,36769,36900,36948,37100,37661,39590,39664,39728,39800,39888,39954,40004,41309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,307", "endColumns": "58,46,145,109", "endOffsets": "109,156,302,412"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "43326,43385,43432,43578", "endColumns": "58,46,145,109", "endOffsets": "43380,43427,43573,43683"}}]}]}