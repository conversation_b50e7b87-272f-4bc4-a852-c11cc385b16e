com.nafiss.user.app-jetified-profileinstaller-1.3.1-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\res
com.nafiss.user.app-jetified-accompanist-themeadapter-core-0.32.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0540263de729c5155ce1a78e37e72993\transformed\jetified-accompanist-themeadapter-core-0.32.0\res
com.nafiss.user.app-jetified-foundation-layout-release-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06934b7ee87ba902cc1c97beba47907c\transformed\jetified-foundation-layout-release\res
com.nafiss.user.app-jetified-animation-core-release-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d145a5b15b5ab9079a5c742530e158\transformed\jetified-animation-core-release\res
com.nafiss.user.app-jetified-IntentSDK-2.4.2-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\res
com.nafiss.user.app-jetified-ui-release-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f9f62d475da5647ccd554e09d9a5175\transformed\jetified-ui-release\res
com.nafiss.user.app-jetified-firebase-messaging-24.1.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\res
com.nafiss.user.app-jetified-material-ripple-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17380c18e82b42e87b2d6dfc71715bd4\transformed\jetified-material-ripple-release\res
com.nafiss.user.app-jetified-payments-ui-core-20.52.3-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\res
com.nafiss.user.app-jetified-activity-compose-1.9.3-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dad94121f24fbe5d0b95c09c16583fa\transformed\jetified-activity-compose-1.9.3\res
com.nafiss.user.app-jetified-material-release-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f0aab3e896844600ee839129612136c\transformed\jetified-material-release\res
com.nafiss.user.app-jetified-uikit-1.31.1-SANDBOX-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\res
com.nafiss.user.app-jetified-stripe-ui-core-20.52.3-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24101954538a7f7c2c85ceec583f6b9b\transformed\jetified-stripe-ui-core-20.52.3\res
com.nafiss.user.app-fragment-1.7.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\260522677f8d78cc30a779d048c78623\transformed\fragment-1.7.1\res
com.nafiss.user.app-jetified-credentials-play-services-auth-1.2.0-rc01-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.nafiss.user.app-localbroadcastmanager-1.1.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27abe7c8fa32e05315b9b266c81703d4\transformed\localbroadcastmanager-1.1.0\res
com.nafiss.user.app-webkit-1.12.1-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\297d281f214de12afec0ec6772c222bb\transformed\webkit-1.12.1\res
com.nafiss.user.app-jetified-stripe-core-20.52.3-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\res
com.nafiss.user.app-jetified-ui-graphics-release-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e029052ced966115f4d17af0300d534\transformed\jetified-ui-graphics-release\res
com.nafiss.user.app-jetified-corekit-1.31.1-SANDBOX-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\res
com.nafiss.user.app-jetified-emoji2-views-helper-1.4.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37de469302e2c446aa8c642cc10d3877\transformed\jetified-emoji2-views-helper-1.4.0\res
com.nafiss.user.app-jetified-lifecycle-runtime-ktx-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\384a3487ef0a0080c58695a386ab607a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.nafiss.user.app-jetified-datastore-preferences-1.0.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b160d8b9da1779072233b65dafb67e1\transformed\jetified-datastore-preferences-1.0.0\res
com.nafiss.user.app-material-1.11.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\res
com.nafiss.user.app-drawerlayout-1.1.1-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f366e0976d4008642dc16217a8811b8\transformed\drawerlayout-1.1.1\res
com.nafiss.user.app-jetified-lifecycle-livedata-core-ktx-2.7.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4017ee38fffd86d5d2bd1c1bd1be11c8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.nafiss.user.app-jetified-core-ktx-1.13.1-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44734e41a66c83c09439a2ee65352add\transformed\jetified-core-ktx-1.13.1\res
com.nafiss.user.app-jetified-issuetracker-1.12.0-SANDBOX-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4618d46e7fa1268b54a29fd46ce2b41c\transformed\jetified-issuetracker-1.12.0-SANDBOX\res
com.nafiss.user.app-jetified-play-services-basement-18.4.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\res
com.nafiss.user.app-navigation-common-ktx-2.7.6-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4940c476a92c0e1ce6707ed6d5a3fab1\transformed\navigation-common-ktx-2.7.6\res
com.nafiss.user.app-jetified-play-services-maps-18.2.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\res
com.nafiss.user.app-navigation-common-2.7.6-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5177fdbad1250f9870a2fbb337983ac8\transformed\navigation-common-2.7.6\res
com.nafiss.user.app-jetified-lifecycle-process-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\res
com.nafiss.user.app-jetified-financial-connections-20.52.3-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\res
com.nafiss.user.app-core-1.13.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\res
com.nafiss.user.app-jetified-play-services-measurement-api-22.1.2-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\res
com.nafiss.user.app-jetified-window-1.2.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\res
com.nafiss.user.app-jetified-firebase-crashlytics-19.3.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\res
com.nafiss.user.app-jetified-animation-release-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6156536e0b56b649058e060194ae134e\transformed\jetified-animation-release\res
com.nafiss.user.app-jetified-emoji2-1.4.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\res
com.nafiss.user.app-jetified-credentials-1.2.0-rc01-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44aaec257ce2cc8cac45c8fbfc80c0\transformed\jetified-credentials-1.2.0-rc01\res
com.nafiss.user.app-jetified-hcaptcha-20.52.3-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5ece3c7dc0bc71837804011c458ad9\transformed\jetified-hcaptcha-20.52.3\res
com.nafiss.user.app-jetified-runtime-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3b47443e0758c24ab25e1a567f9ffd\transformed\jetified-runtime-release\res
com.nafiss.user.app-jetified-payments-model-20.52.3-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d20986c655cfa164c65383bfcf47f6d\transformed\jetified-payments-model-20.52.3\res
com.nafiss.user.app-jetified-accompanist-themeadapter-material-0.32.0-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edca040889e79ca9b38ba901c6a0fa7\transformed\jetified-accompanist-themeadapter-material-0.32.0\res
com.nafiss.user.app-preference-1.2.1-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f63fc8e219d0da2c2381781a446b35d\transformed\preference-1.2.1\res
com.nafiss.user.app-jetified-lifecycle-livedata-ktx-2.7.0-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71741ae3608a014bf71abddc3430cd76\transformed\jetified-lifecycle-livedata-ktx-2.7.0\res
com.nafiss.user.app-navigation-runtime-2.7.6-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c79ad1cc2e89996ad18f6440f6c925\transformed\navigation-runtime-2.7.6\res
com.nafiss.user.app-jetified-foundation-release-48 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\734287ca7a5c3cecf4ed3a5d2b4dd2a4\transformed\jetified-foundation-release\res
com.nafiss.user.app-jetified-link-20.52.3-49 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\res
com.nafiss.user.app-navigation-runtime-ktx-2.7.6-50 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7643d420cb7f0d645ee669af01e246f4\transformed\navigation-runtime-ktx-2.7.6\res
com.nafiss.user.app-appcompat-1.6.1-51 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c68162bc9aa811289901f5e22f4653a\transformed\appcompat-1.6.1\res
com.nafiss.user.app-jetified-play-services-wallet-19.3.0-52 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c37758e4c632d9b51d3452f8c90e3\transformed\jetified-play-services-wallet-19.3.0\res
com.nafiss.user.app-jetified-ui-geometry-release-53 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83284b1ca1734e1de536bf196acf215e\transformed\jetified-ui-geometry-release\res
com.nafiss.user.app-slidingpanelayout-1.2.0-54 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\876e4ff582ebdfeddfd9bca2d49466dd\transformed\slidingpanelayout-1.2.0\res
com.nafiss.user.app-jetified-lifecycle-viewmodel-compose-2.7.0-55 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8933da7794439b520a0a7d7c44b67732\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\res
com.nafiss.user.app-browser-1.8.0-56 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a6b9eb336c3d2a2816400f2435a9ad9\transformed\browser-1.8.0\res
com.nafiss.user.app-coordinatorlayout-1.1.0-57 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad783780d62bb70d3b31349f8da5d7\transformed\coordinatorlayout-1.1.0\res
com.nafiss.user.app-jetified-stripe-3ds2-android-6.1.8-58 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\res
com.nafiss.user.app-jetified-startup-runtime-1.1.1-59 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\res
com.nafiss.user.app-jetified-navigation-compose-2.7.6-60 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9744ea7d593db4848481b2dc1ca0ed99\transformed\jetified-navigation-compose-2.7.6\res
com.nafiss.user.app-jetified-activity-ktx-1.9.3-61 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97bc3aba17b8e77b2a7cadcc02faec9f\transformed\jetified-activity-ktx-1.9.3\res
com.nafiss.user.app-jetified-fragment-ktx-1.7.1-62 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98de64f0111d22d1533700e71a117eb1\transformed\jetified-fragment-ktx-1.7.1\res
com.nafiss.user.app-jetified-tracing-1.2.0-63 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d2725a8f52f2f1b7f5df24dbfcf06b9\transformed\jetified-tracing-1.2.0\res
com.nafiss.user.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-64 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e083c77bf2774c552dcb395d84f66a5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.nafiss.user.app-jetified-glide-4.12.0-65 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\res
com.nafiss.user.app-recyclerview-1.3.2-66 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f9e9b6b52770bd44d0b075f702cb31a\transformed\recyclerview-1.3.2\res
com.nafiss.user.app-jetified-lifecycle-service-2.7.0-67 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0780b40bae4a82b2ae549deaa1d2d7a\transformed\jetified-lifecycle-service-2.7.0\res
com.nafiss.user.app-transition-1.4.1-68 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d3e3f1bd8a48a8aaf1db8287bb1341\transformed\transition-1.4.1\res
com.nafiss.user.app-jetified-material-icons-core-release-69 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20044f6c5f7a971de13e8ca5eb00b3f\transformed\jetified-material-icons-core-release\res
com.nafiss.user.app-media-1.1.0-70 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4dccb56f227b56afcf95779216ccc46\transformed\media-1.1.0\res
com.nafiss.user.app-core-runtime-2.2.0-71 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54ac26a17e1996f67c0683b84824bbf\transformed\core-runtime-2.2.0\res
com.nafiss.user.app-jetified-ui-tooling-preview-release-72 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591a2f912f2642b7c3ce93cb5c31cb2\transformed\jetified-ui-tooling-preview-release\res
com.nafiss.user.app-jetified-play-services-auth-21.0.0-73 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\res
com.nafiss.user.app-jetified-customview-poolingcontainer-1.0.0-74 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a624f6169bd423a64f257c4af3ec9f18\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.nafiss.user.app-jetified-play-services-base-18.5.0-75 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\res
com.nafiss.user.app-constraintlayout-2.1.4-76 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\res
com.nafiss.user.app-jetified-datastore-1.0.0-77 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adbfae24b25496a769a0de168ef10463\transformed\jetified-datastore-1.0.0\res
com.nafiss.user.app-jetified-ui-unit-release-78 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae06639c40c37163a8dbcecf01e73766\transformed\jetified-ui-unit-release\res
com.nafiss.user.app-jetified-runtime-livedata-1.5.4-79 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aebe4dd8179f1aa94b7c979bec7ed9c5\transformed\jetified-runtime-livedata-1.5.4\res
com.nafiss.user.app-swiperefreshlayout-1.1.0-80 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aec37582522615c6d6916d078a5ccf97\transformed\swiperefreshlayout-1.1.0\res
com.nafiss.user.app-jetified-swipe-layout-android-1.0.17-81 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\res
com.nafiss.user.app-cardview-1.0.0-82 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb57c3e5a03a182263a89edddce573a7\transformed\cardview-1.0.0\res
com.nafiss.user.app-jetified-analytics-1.7.1-SANDBOX-83 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\res
com.nafiss.user.app-jetified-better-link-movement-method-2.2.0-84 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf62539f90a6d2a15f0bacfd65e04f94\transformed\jetified-better-link-movement-method-2.2.0\res
com.nafiss.user.app-jetified-firebase-common-21.0.0-85 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\res
com.nafiss.user.app-jetified-activity-1.9.3-86 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c11d81441550d0689fe1a67fbc28a1\transformed\jetified-activity-1.9.3\res
com.nafiss.user.app-jetified-material3-1.0.1-87 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6580ba3899d752f0adcab81246e19f4\transformed\jetified-material3-1.0.1\res
com.nafiss.user.app-jetified-core-1.0.0-88 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caccccc897ccf3cd2ffaeaa238014d9f\transformed\jetified-core-1.0.0\res
com.nafiss.user.app-jetified-ui-viewbinding-1.5.4-89 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb23a5c46c3e34a7c7847fc03bf5fc3c\transformed\jetified-ui-viewbinding-1.5.4\res
com.nafiss.user.app-lifecycle-livedata-core-2.7.0-90 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb45c187cc4bf07f326acb55403dced5\transformed\lifecycle-livedata-core-2.7.0\res
com.nafiss.user.app-jetified-core-common-2.0.3-91 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\res
com.nafiss.user.app-jetified-lifecycle-viewmodel-ktx-2.7.0-92 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0f12582c452eb691a8e44b0e8084bba\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.nafiss.user.app-jetified-accompanist-themeadapter-material3-0.32.0-93 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d428d89fcf3fdd09e53438e0ac46f10a\transformed\jetified-accompanist-themeadapter-material3-0.32.0\res
com.nafiss.user.app-lifecycle-runtime-2.7.0-94 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d60b9dfcc626ec6500a5bf1bb597609c\transformed\lifecycle-runtime-2.7.0\res
com.nafiss.user.app-jetified-savedstate-ktx-1.2.1-95 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da03bed7a3bc1708d07957d8daac4d73\transformed\jetified-savedstate-ktx-1.2.1\res
com.nafiss.user.app-jetified-annotation-experimental-1.4.1-96 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db0890d196505bd23acf50fce22364ef\transformed\jetified-annotation-experimental-1.4.1\res
com.nafiss.user.app-jetified-runtime-saveable-release-97 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db920e7c324fc81d84865df5ad7793be\transformed\jetified-runtime-saveable-release\res
com.nafiss.user.app-jetified-ads-adservices-java-1.0.0-beta05-98 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbddd8ac3be61af4933f58e5538a8bdb\transformed\jetified-ads-adservices-java-1.0.0-beta05\res
com.nafiss.user.app-jetified-SmsVerifyCatcher-0.3.3-99 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\res
com.nafiss.user.app-jetified-accompanist-themeadapter-appcompat-0.32.0-100 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e159e2c46b78cc2c8453cf6e92d6dbbf\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\res
com.nafiss.user.app-jetified-ui-util-release-101 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e49c3ea676e22abc40aa6676849933c4\transformed\jetified-ui-util-release\res
com.nafiss.user.app-jetified-android-maps-utils-3.6.0-102 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\res
com.nafiss.user.app-jetified-window-java-1.2.0-103 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9ae86c85487d86163dc59b9985eb1b1\transformed\jetified-window-java-1.2.0\res
com.nafiss.user.app-jetified-payments-core-20.52.3-104 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\res
com.nafiss.user.app-jetified-ads-adservices-1.0.0-beta05-105 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\res
com.nafiss.user.app-lifecycle-viewmodel-2.7.0-106 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0f9724190e07e8737aaa267698c9de2\transformed\lifecycle-viewmodel-2.7.0\res
com.nafiss.user.app-jetified-viewpager2-1.1.0-beta02-107 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29be1fa7446a69f224d9ea40229e93c\transformed\jetified-viewpager2-1.1.0-beta02\res
com.nafiss.user.app-jetified-paymentsheet-20.52.3-108 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\res
com.nafiss.user.app-jetified-appcompat-resources-1.6.1-109 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43c94eb5a9be4d13bce209dd2e0f08b\transformed\jetified-appcompat-resources-1.6.1\res
com.nafiss.user.app-lifecycle-livedata-2.7.0-110 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4c7bd69a33da4ec1eccd1f1916102f3\transformed\lifecycle-livedata-2.7.0\res
com.nafiss.user.app-jetified-zxing-android-embedded-3.5.0-111 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\res
com.nafiss.user.app-jetified-ui-text-release-112 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc5c87a03af947d6c14df0602dee0471\transformed\jetified-ui-text-release\res
com.nafiss.user.app-jetified-savedstate-1.2.1-113 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2e5b5c6f8d61eda7736195ad612c0c\transformed\jetified-savedstate-1.2.1\res
com.nafiss.user.app-debug-114 C:\wamp64\www\user_app\android\app\src\debug\res
com.nafiss.user.app-main-115 C:\wamp64\www\user_app\android\app\src\main\res
com.nafiss.user.app-res-116 C:\wamp64\www\user_app\build\app\generated\crashlytics\res\debug
com.nafiss.user.app-google-services-117 C:\wamp64\www\user_app\build\app\generated\res\google-services\debug
com.nafiss.user.app-pngs-118 C:\wamp64\www\user_app\build\app\generated\res\pngs\debug
com.nafiss.user.app-resValues-119 C:\wamp64\www\user_app\build\app\generated\res\resValues\debug
com.nafiss.user.app-packageDebugResources-120 C:\wamp64\www\user_app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.nafiss.user.app-packageDebugResources-121 C:\wamp64\www\user_app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.nafiss.user.app-debug-122 C:\wamp64\www\user_app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.nafiss.user.app-debug-123 C:\wamp64\www\user_app\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-124 C:\wamp64\www\user_app\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-125 C:\wamp64\www\user_app\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-126 C:\wamp64\www\user_app\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-127 C:\wamp64\www\user_app\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-128 C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-129 C:\wamp64\www\user_app\build\firebase_database\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-130 C:\wamp64\www\user_app\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-131 C:\wamp64\www\user_app\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-132 C:\wamp64\www\user_app\build\flutter_custom_tabs_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-133 C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-134 C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-135 C:\wamp64\www\user_app\build\flutter_paystack\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-136 C:\wamp64\www\user_app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-137 C:\wamp64\www\user_app\build\fluttertoast\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-138 C:\wamp64\www\user_app\build\geocoding_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-139 C:\wamp64\www\user_app\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-140 C:\wamp64\www\user_app\build\google_maps_flutter_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-141 C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-142 C:\wamp64\www\user_app\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-143 C:\wamp64\www\user_app\build\midpay\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-144 C:\wamp64\www\user_app\build\nb_utils\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-145 C:\wamp64\www\user_app\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-146 C:\wamp64\www\user_app\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-147 C:\wamp64\www\user_app\build\phonepe_payment_sdk\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-148 C:\wamp64\www\user_app\build\share_plus\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-149 C:\wamp64\www\user_app\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-150 C:\wamp64\www\user_app\build\speech_to_text\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-151 C:\wamp64\www\user_app\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-152 C:\wamp64\www\user_app\build\stripe_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-153 C:\wamp64\www\user_app\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
com.nafiss.user.app-debug-154 C:\wamp64\www\user_app\build\webview_flutter_android\intermediates\packaged_res\debug\packageDebugResources
