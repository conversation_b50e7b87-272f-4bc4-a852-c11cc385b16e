{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-ca-rES/values-ca-rES.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,203,270,354,426,487,559,627,689,757,827,887,948,1022,1086,1154,1217,1285,1349,1432,1508,1592,1691,1809,1898,1948,2007,2095,2159,2228,2297,2413,2500,2608", "endColumns": "63,83,66,83,71,60,71,67,61,67,69,59,60,73,63,67,62,67,63,82,75,83,98,117,88,49,58,87,63,68,68,115,86,107,106", "endOffsets": "114,198,265,349,421,482,554,622,684,752,822,882,943,1017,1081,1149,1212,1280,1344,1427,1503,1587,1686,1804,1893,1943,2002,2090,2154,2223,2292,2408,2495,2603,2710"}, "to": {"startLines": "16,19,22,24,25,26,33,34,36,37,38,39,40,41,42,44,45,48,59,60,72,74,75,109,110,122,133,134,136,143,144,153,154,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1124,1376,1649,1794,1878,1950,2439,2511,2650,2712,2780,2850,2910,2971,3045,3167,3235,3478,4366,4430,5553,5722,5806,10509,10627,11819,12623,12682,12823,13601,13670,14399,14515,15258,15366", "endColumns": "63,83,66,83,71,60,71,67,61,67,69,59,60,73,63,67,62,67,63,82,75,83,98,117,88,49,58,87,63,68,68,115,86,107,106", "endOffsets": "1183,1455,1711,1873,1945,2006,2506,2574,2707,2775,2845,2905,2966,3040,3104,3230,3293,3541,4425,4508,5624,5801,5900,10622,10711,11864,12677,12765,12882,13665,13734,14510,14597,15361,15468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,351,435,493,552,603,698,781,875,979,1083,1168,1246,1327,1408,1508,1601,1671,1761,1849,1938,2047,2127,2214,2297,2400,2491,2604,2699,2790,2892,3010,3100,3193,3300,3400,3470,3535,4339,5094,5173,5298,5408,5463,5564,5661,5742,5832,5937,5993,6080,6132,6213,6329,6409,6482,6549,6615,6662,6747,6851,6895,6942,7017,7077,7141,7341,7504,7656,7727,7816,7897,7981,8078,8177,8251,8344,8427,8529,8615,8713,8770,8906,8957,9012,9077,9148,9220,9289,9364,9460,9536,9589", "endColumns": "74,83,61,74,83,57,58,50,94,82,93,103,103,84,77,80,80,99,92,69,89,87,88,108,79,86,82,102,90,112,94,90,101,117,89,92,106,99,69,64,803,754,78,124,109,54,100,96,80,89,104,55,86,51,80,115,79,72,66,65,46,84,103,43,46,74,59,63,199,162,151,70,88,80,83,96,98,73,92,82,101,85,97,56,135,50,54,64,70,71,68,74,95,75,52,84", "endOffsets": "125,209,271,346,430,488,547,598,693,776,870,974,1078,1163,1241,1322,1403,1503,1596,1666,1756,1844,1933,2042,2122,2209,2292,2395,2486,2599,2694,2785,2887,3005,3095,3188,3295,3395,3465,3530,4334,5089,5168,5293,5403,5458,5559,5656,5737,5827,5932,5988,6075,6127,6208,6324,6404,6477,6544,6610,6657,6742,6846,6890,6937,7012,7072,7136,7336,7499,7651,7722,7811,7892,7976,8073,8172,8246,8339,8422,8524,8610,8708,8765,8901,8952,9007,9072,9143,9215,9284,9359,9455,9531,9584,9669"}, "to": {"startLines": "2,3,4,5,6,7,8,12,13,14,15,18,20,21,23,28,32,47,50,51,52,54,55,56,58,62,63,64,65,66,67,68,69,70,71,73,76,77,83,84,85,96,97,98,99,100,101,102,103,104,105,106,107,114,115,116,117,118,119,120,124,129,130,131,132,137,138,139,140,141,142,146,147,156,157,159,160,164,165,167,172,179,180,241,242,243,245,250,261,262,263,264,265,266,267,282", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,351,435,493,801,852,947,1030,1272,1460,1564,1716,2072,2358,3378,3619,3712,3782,3940,4028,4117,4286,4580,4667,4750,4853,4944,5057,5152,5243,5345,5463,5629,5905,6012,6805,6875,6940,8696,9451,9530,9655,9765,9820,9921,10018,10099,10189,10294,10350,10989,11041,11122,11238,11318,11391,11458,11966,12343,12428,12532,12576,12887,12962,13022,13086,13286,13449,13792,13863,14728,14809,14972,15069,15473,15547,15709,16335,16886,16972,23401,23458,23594,23752,24326,26307,26378,26450,26519,26594,26690,26766,28121", "endColumns": "74,83,61,74,83,57,58,50,94,82,93,103,103,84,77,80,80,99,92,69,89,87,88,108,79,86,82,102,90,112,94,90,101,117,89,92,106,99,69,64,803,754,78,124,109,54,100,96,80,89,104,55,86,51,80,115,79,72,66,65,46,84,103,43,46,74,59,63,199,162,151,70,88,80,83,96,98,73,92,82,101,85,97,56,135,50,54,64,70,71,68,74,95,75,52,84", "endOffsets": "125,209,271,346,430,488,547,847,942,1025,1119,1371,1559,1644,1789,2148,2434,3473,3707,3777,3867,4023,4112,4221,4361,4662,4745,4848,4939,5052,5147,5238,5340,5458,5548,5717,6007,6107,6870,6935,7739,9446,9525,9650,9760,9815,9916,10013,10094,10184,10289,10345,10432,11036,11117,11233,11313,11386,11453,11519,12008,12423,12527,12571,12618,12957,13017,13081,13281,13444,13596,13858,13947,14804,14888,15064,15163,15542,15635,15787,16432,16967,17065,23453,23589,23640,23802,24386,26373,26445,26514,26589,26685,26761,26814,28201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,339,405,469,569,629,688,764,867,1038,1313,1606,1684,1754,1843,1940,2060,2189,2258,2332,2495,2564,2626,2703,2786,2926,3044,3152,3243,3320,3409,3485", "endColumns": "73,83,125,65,63,99,59,58,75,102,170,274,292,77,69,88,96,119,128,68,73,162,68,61,76,82,139,117,107,90,76,88,75,161", "endOffsets": "124,208,334,400,464,564,624,683,759,862,1033,1308,1601,1679,1749,1838,1935,2055,2184,2253,2327,2490,2559,2621,2698,2781,2921,3039,3147,3238,3315,3404,3480,3642"}, "to": {"startLines": "9,11,155,173,178,240,253,254,255,256,257,258,259,274,275,276,277,278,279,280,281,284,285,286,287,288,289,290,291,292,293,294,295,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "552,717,14602,16437,16822,23301,25193,25253,25312,25388,25491,25662,25937,27395,27473,27543,27632,27729,27849,27978,28047,28266,28429,28498,28560,28637,28720,28860,28978,29086,29177,29254,29343,29419", "endColumns": "73,83,125,65,63,99,59,58,75,102,170,274,292,77,69,88,96,119,128,68,73,162,68,61,76,82,139,117,107,90,76,88,75,161", "endOffsets": "621,796,14723,16498,16881,23396,25248,25307,25383,25486,25657,25932,26225,27468,27538,27627,27724,27844,27973,28042,28116,28424,28493,28555,28632,28715,28855,28973,29081,29172,29249,29338,29414,29576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,243,332,415,498,652,749,848,935,1067,1238,1434,1585,1674,1785,1877,1978,2180,2287,2389,2668,2778,2979,3078,3138,3203,3292,3394,3500,3620,3847,3914,3984,4062,4143,4232,4299,4385,4456,4523,4640,4728,4845,4945,5024,5117,5187,5281,5339,5445,5553,5657,5785,5845,5948,6060,6131,6238,6315,6406,6479,6601,6727,6833", "endColumns": "90,96,88,82,82,153,96,98,86,131,170,195,150,88,110,91,100,201,106,101,278,109,200,98,59,64,88,101,105,119,226,66,69,77,80,88,66,85,70,66,116,87,116,99,78,92,69,93,57,105,107,103,127,59,102,111,70,106,76,90,72,121,125,105,59", "endOffsets": "141,238,327,410,493,647,744,843,930,1062,1233,1429,1580,1669,1780,1872,1973,2175,2282,2384,2663,2773,2974,3073,3133,3198,3287,3389,3495,3615,3842,3909,3979,4057,4138,4227,4294,4380,4451,4518,4635,4723,4840,4940,5019,5112,5182,5276,5334,5440,5548,5652,5780,5840,5943,6055,6126,6233,6310,6401,6474,6596,6722,6828,6888"}, "to": {"startLines": "10,81,93,94,95,112,123,125,126,175,182,183,184,187,189,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,244,260,269,270,271,272,273,283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "626,6490,8441,8530,8613,10771,11869,12013,12112,16568,17130,17301,17497,17806,18001,18215,18307,18408,18610,18717,18819,19098,19297,19498,19597,19657,19722,19811,19913,20019,20139,20366,20433,20503,20581,20662,20751,20818,20904,20975,21042,21606,21694,21811,21911,21990,22083,22153,22247,22305,22411,22519,22623,22751,22811,22914,23026,23645,26230,26877,26968,27041,27163,27289,28206", "endColumns": "90,96,88,82,82,153,96,98,86,131,170,195,150,88,110,91,100,201,106,101,278,109,200,98,59,64,88,101,105,119,226,66,69,77,80,88,66,85,70,66,116,87,116,99,78,92,69,93,57,105,107,103,127,59,102,111,70,106,76,90,72,121,125,105,59", "endOffsets": "712,6582,8525,8608,8691,10920,11961,12107,12194,16695,17296,17492,17643,17890,18107,18302,18403,18605,18712,18814,19093,19203,19493,19592,19652,19717,19806,19908,20014,20134,20361,20428,20498,20576,20657,20746,20813,20899,20970,21037,21154,21689,21806,21906,21985,22078,22148,22242,22300,22406,22514,22618,22746,22806,22909,23021,23092,23747,26302,26963,27036,27158,27284,27390,28261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,200,262,342,405,476,534,614,687,755,815", "endColumns": "83,60,61,79,62,70,57,79,72,67,59,66", "endOffsets": "134,195,257,337,400,471,529,609,682,750,810,877"}, "to": {"startLines": "17,27,29,30,31,35,43,46,49,53,57,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1188,2011,2153,2215,2295,2579,3109,3298,3546,3872,4226,4513", "endColumns": "83,60,61,79,62,70,57,79,72,67,59,66", "endOffsets": "1267,2067,2210,2290,2353,2645,3162,3373,3614,3935,4281,4575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-ca-rES\\values-ca-rES.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,331,433,651,698,770,877,951,1187,1259,1348,1420,1475,1539,1834,1912,1978,2031,2084,2156,2270,2388,2445,2531,2610,2700,2769,2872,3153,3235,3312,3377,3437,3499,3559,3631,3717,3823,3926,4015,4122,4215,4291,4370,4462,4666,4881,4995,5122,5185,5920,5987", "endColumns": "116,158,101,217,46,71,106,73,235,71,88,71,54,63,294,77,65,52,52,71,113,117,56,85,78,89,68,102,280,81,76,64,59,61,59,71,85,105,102,88,106,92,75,78,91,203,214,113,126,62,734,66,57", "endOffsets": "167,326,428,646,693,765,872,946,1182,1254,1343,1415,1470,1534,1829,1907,1973,2026,2079,2151,2265,2383,2440,2526,2605,2695,2764,2867,3148,3230,3307,3372,3432,3494,3554,3626,3712,3818,3921,4010,4117,4210,4286,4365,4457,4661,4876,4990,5117,5180,5915,5982,6040"}, "to": {"startLines": "78,79,80,82,86,87,88,89,90,91,92,108,111,113,121,127,128,135,145,148,149,150,151,152,158,161,166,168,169,170,171,174,176,177,181,185,186,188,190,198,218,219,220,221,222,239,246,247,248,249,251,252,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6112,6229,6388,6587,7744,7791,7863,7970,8044,8280,8352,10437,10716,10925,11524,12199,12277,12770,13739,13952,14024,14138,14256,14313,14893,15168,15640,15792,15895,16176,16258,16503,16700,16760,17070,17648,17720,17895,18112,19208,21159,21266,21359,21435,21514,23097,23807,24022,24136,24263,24391,25126,26819", "endColumns": "116,158,101,217,46,71,106,73,235,71,88,71,54,63,294,77,65,52,52,71,113,117,56,85,78,89,68,102,280,81,76,64,59,61,59,71,85,105,102,88,106,92,75,78,91,203,214,113,126,62,734,66,57", "endOffsets": "6224,6383,6485,6800,7786,7858,7965,8039,8275,8347,8436,10504,10766,10984,11814,12272,12338,12818,13787,14019,14133,14251,14308,14394,14967,15253,15704,15890,16171,16253,16330,16563,16755,16817,17125,17715,17801,17996,18210,19292,21261,21354,21430,21509,21601,23296,24017,24131,24258,24321,25121,25188,26872"}}]}]}