{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,254,360,443,526,668,777,881,956,1075,1253,1466,1616,1707,1805,1900,1996,2199,2312,2409,2659,2757,2960,3052,3114,3182,3273,3366,3465,3569,3784,3858,3927,4002,4086,4171,4242,4326,4400,4477,4625,4717,4825,4923,4997,5078,5147,5233,5296,5400,5535,5646,5780,5844,5937,6035,6106,6217,6290,6387,6460,6590,6708,6804", "endColumns": "89,108,105,82,82,141,108,103,74,118,177,212,149,90,97,94,95,202,112,96,249,97,202,91,61,67,90,92,98,103,214,73,68,74,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,70,110,72,96,72,129,117,95,57", "endOffsets": "140,249,355,438,521,663,772,876,951,1070,1248,1461,1611,1702,1800,1895,1991,2194,2307,2404,2654,2752,2955,3047,3109,3177,3268,3361,3460,3564,3779,3853,3922,3997,4081,4166,4237,4321,4395,4472,4620,4712,4820,4918,4992,5073,5142,5228,5291,5395,5530,5641,5775,5839,5932,6030,6101,6212,6285,6382,6455,6585,6703,6799,6857"}, "to": {"startLines": "173,244,256,257,258,275,286,288,289,339,346,347,348,351,353,355,356,357,358,359,360,361,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,408,424,433,434,435,436,437,447", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15563,21481,23387,23493,23576,25585,26681,26839,26943,31483,32041,32219,32432,32736,32934,33131,33226,33322,33525,33638,33735,33985,34184,34387,34479,34541,34609,34700,34793,34892,34996,35211,35285,35354,35429,35513,35598,35669,35753,35827,35904,36528,36620,36728,36826,36900,36981,37050,37136,37199,37303,37438,37549,37683,37747,37840,37938,38531,41219,41843,41940,42013,42143,42261,43173", "endColumns": "89,108,105,82,82,141,108,103,74,118,177,212,149,90,97,94,95,202,112,96,249,97,202,91,61,67,90,92,98,103,214,73,68,74,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,70,110,72,96,72,129,117,95,57", "endOffsets": "15648,21585,23488,23571,23654,25722,26785,26938,27013,31597,32214,32427,32577,32822,33027,33221,33317,33520,33633,33730,33980,34078,34382,34474,34536,34604,34695,34788,34887,34991,35206,35280,35349,35424,35508,35593,35664,35748,35822,35899,36047,36615,36723,36821,36895,36976,37045,37131,37194,37298,37433,37544,37678,37742,37835,37933,38004,38637,41287,41935,42008,42138,42256,42352,43226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,79,82,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3377,3455,3533,3617,3715,4533,4630,4767,7940,8176,8658,8907,8970,9088,9149,9214,9271,9341,9402,9456,9572,9629,9691,9745,9819,9947,10035,10121,10258,10342,10427,10561,10652,10728,10782,10833,10899,10971,11049,11145,11227,11307,11383,11460,11537,11644,11733,11806,11896,11991,12065,12146,12239,12294,12375,12441,12527,12612,12674,12738,12801,12873,12971,13070,13165,13257,13315,13758", "endLines": "7,37,38,39,40,41,49,50,51,79,82,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "426,3450,3528,3612,3710,3801,4625,4762,4854,8001,8270,8730,8965,9083,9144,9209,9266,9336,9397,9451,9567,9624,9686,9740,9814,9942,10030,10116,10253,10337,10422,10556,10647,10723,10777,10828,10894,10966,11044,11140,11222,11302,11378,11455,11532,11639,11728,11801,11891,11986,12060,12141,12234,12289,12370,12436,12522,12607,12669,12733,12796,12868,12966,13065,13160,13252,13310,13365,13833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "74,83,84,85", "startColumns": "4,4,4,4", "startOffsets": "7459,8275,8380,8492", "endColumns": "107,104,111,104", "endOffsets": "7562,8375,8487,8592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,209,352,409,482,574,633,696,798,906,1092,1388,1699,1793,1865,1944,2034,2165,2300,2365,2441,2627,2694,2757,2829,2912,3029,3153,3262,3351,3440,3528,3607", "endColumns": "76,76,142,56,72,91,58,62,101,107,185,295,310,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,108,88,88,87,78,156", "endOffsets": "127,204,347,404,477,569,628,691,793,901,1087,1383,1694,1788,1860,1939,2029,2160,2295,2360,2436,2622,2689,2752,2824,2907,3024,3148,3257,3346,3435,3523,3602,3759"}, "to": {"startLines": "172,174,319,337,342,404,417,418,419,420,421,422,423,438,439,440,441,442,443,444,445,448,449,450,451,452,453,454,455,456,457,458,459,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15486,15653,29477,31344,31727,38196,40094,40153,40216,40318,40426,40612,40908,42357,42451,42523,42602,42692,42823,42958,43023,43231,43417,43484,43547,43619,43702,43819,43943,44052,44141,44230,44318,44397", "endColumns": "76,76,142,56,72,91,58,62,101,107,185,295,310,93,71,78,89,130,134,64,75,185,66,62,71,82,116,123,108,88,88,87,78,156", "endOffsets": "15558,15725,29615,31396,31795,38283,40148,40211,40313,40421,40607,40903,41214,42446,42518,42597,42687,42818,42953,43018,43094,43412,43479,43542,43614,43697,43814,43938,44047,44136,44225,44313,44392,44549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,157", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3806,3904,4006,4107,4208,4313,4416,14308", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3899,4001,4102,4203,4308,4411,4528,14404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "86,467", "startColumns": "4,4", "startOffsets": "8597,45160", "endColumns": "60,78", "endOffsets": "8653,45234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6111", "endColumns": "156", "endOffsets": "6263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3399,3470,3534,4277,4954,5028,5134,5241,5296,5385,5471,5540,5627,5729,5785,5875,5924,6001,6108,6173,6244,6311,6377,6426,6506,6598,6645,6699,6773,6831,6910,7089,7244,7377,7444,7538,7617,7714,7797,7878,7954,8043,8125,8233,8320,8414,8470,8607,8657,8712,8801,8868,8937,9007,9076,9164,9235,9286", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3394,3465,3529,4272,4949,5023,5129,5236,5291,5380,5466,5535,5622,5724,5780,5870,5919,5996,6103,6168,6239,6306,6372,6421,6501,6593,6640,6694,6768,6826,6905,7084,7239,7372,7439,7533,7612,7709,7792,7873,7949,8038,8120,8228,8315,8409,8465,8602,8652,8707,8796,8863,8932,9002,9071,9159,9230,9281,9355"}, "to": {"startLines": "165,166,167,168,169,170,171,175,176,177,178,181,183,184,186,191,195,210,213,214,215,217,218,219,221,225,226,227,228,229,230,231,232,233,234,236,239,240,246,247,248,259,260,261,262,263,264,265,266,267,268,269,270,277,278,279,280,281,282,283,287,292,293,294,295,300,301,302,303,304,305,309,310,320,321,323,324,328,329,331,336,343,344,405,406,407,409,414,425,426,427,428,429,430,431,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14955,15024,15102,15172,15254,15350,15415,15730,15785,15856,15945,16162,16347,16452,16607,16963,17262,18326,18593,18695,18769,18936,19029,19119,19294,19604,19703,19784,19890,19964,20057,20162,20256,20350,20451,20593,20850,20966,21802,21873,21937,23659,24336,24410,24516,24623,24678,24767,24853,24922,25009,25111,25167,25791,25840,25917,26024,26089,26160,26227,26790,27159,27239,27331,27378,27686,27760,27818,27897,28076,28231,28563,28630,29620,29699,29878,29961,30325,30401,30575,31236,31800,31887,38288,38344,38481,38642,39234,41292,41359,41428,41498,41567,41655,41726,43099", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "15019,15097,15167,15249,15345,15410,15481,15780,15851,15940,16014,16262,16447,16535,16684,17043,17347,18423,18690,18764,18863,19024,19114,19229,19374,19698,19779,19885,19959,20052,20157,20251,20345,20446,20512,20659,20961,21070,21868,21932,22675,24331,24405,24511,24618,24673,24762,24848,24917,25004,25106,25162,25252,25835,25912,26019,26084,26155,26222,26288,26834,27234,27326,27373,27427,27755,27813,27892,28071,28226,28359,28625,28719,29694,29791,29956,30037,30396,30485,30652,31339,31882,31976,38339,38476,38526,38692,39318,41354,41423,41493,41562,41650,41721,41772,43168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "180,190,192,193,194,198,206,209,212,216,220,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16082,16903,17048,17112,17197,17488,18054,18249,18516,18868,19234,19530", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "16157,16958,17107,17192,17257,17555,18107,18321,18588,18931,19289,19599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "28724", "endColumns": "91", "endOffsets": "28811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02ee5bfe99bcfa22576a7c61152b85ef\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,117,164,306", "endColumns": "61,46,141,125", "endOffsets": "112,159,301,427"}, "to": {"startLines": "468,469,470,471", "startColumns": "4,4,4,4", "startOffsets": "45239,45301,45348,45490", "endColumns": "61,46,141,125", "endOffsets": "45296,45343,45485,45611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,359,461,673,719,786,885,960,1228,1288,1380,1459,1513,1577,1914,1987,2055,2108,2161,2240,2355,2466,2533,2612,2694,2778,2863,2982,3276,3365,3442,3524,3584,3649,3709,3781,3863,3970,4069,4170,4299,4397,4471,4550,4646,4833,5044,5175,5307,5370,6077,6141", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,63,65", "endOffsets": "179,354,456,668,714,781,880,955,1223,1283,1375,1454,1508,1572,1909,1982,2050,2103,2156,2235,2350,2461,2528,2607,2689,2773,2858,2977,3271,3360,3437,3519,3579,3644,3704,3776,3858,3965,4064,4165,4294,4392,4466,4545,4641,4828,5039,5170,5302,5365,6072,6136,6202"}, "to": {"startLines": "241,242,243,245,249,250,251,252,253,254,255,271,274,276,284,290,291,298,308,312,313,314,315,316,322,325,330,332,333,334,335,338,340,341,345,349,350,352,354,362,382,383,384,385,386,403,410,411,412,413,415,416,432", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21075,21204,21379,21590,22680,22726,22793,22892,22967,23235,23295,25257,25531,25727,26293,27018,27091,27569,28510,28816,28895,29010,29121,29188,29796,30042,30490,30657,30776,31070,31159,31401,31602,31662,31981,32582,32654,32827,33032,34083,36052,36181,36279,36353,36432,38009,38697,38908,39039,39171,39323,40030,41777", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,63,65", "endOffsets": "21199,21374,21476,21797,22721,22788,22887,22962,23230,23290,23382,25331,25580,25786,26625,27086,27154,27617,28558,28890,29005,29116,29183,29262,29873,30121,30570,30771,31065,31154,31231,31478,31657,31722,32036,32649,32731,32929,33126,34179,36176,36274,36348,36427,36523,38191,38903,39034,39166,39229,40025,40089,41838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,14154", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,14231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5112,5219,5385,5511,5621,5763,5892,6007,6268,6449,6556,6719,6845,7012,7170,7239,7299", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "5214,5380,5506,5616,5758,5887,6002,6106,6444,6551,6714,6840,7007,7165,7234,7294,7380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14409,14495,14598,14665,14732,14796,14883", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "14490,14593,14660,14727,14791,14878,14950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3148,3258", "endColumns": "109,118", "endOffsets": "3253,3372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "73,80,148,152,461,465,466", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7385,8006,13540,13838,44554,45000,45082", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "7454,8093,13612,13978,44718,45077,45155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "54,77,81", "startColumns": "4,4,4", "startOffsets": "5035,7767,8098", "endColumns": "76,80,77", "endOffsets": "5107,7843,8171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "52,53,75,76,78,88,89,146,147,149,150,153,154,156,462,463,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4859,4952,7567,7665,7848,8735,8817,13370,13458,13617,13688,13983,14067,14236,44723,44807,44877", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4947,5030,7660,7762,7935,8812,8902,13453,13535,13683,13753,14062,14149,14303,44802,44872,44995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "179,182,185,187,188,189,196,197,199,200,201,202,203,204,205,207,208,211,222,223,235,237,238,272,273,285,296,297,299,306,307,317,318,326,327", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16019,16267,16540,16689,16773,16842,17352,17424,17560,17625,17711,17781,17853,17916,17990,18112,18185,18428,19379,19444,20517,20664,20754,25336,25450,26630,27432,27483,27622,28364,28436,29267,29383,30126,30227", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "16077,16342,16602,16768,16837,16898,17419,17483,17620,17706,17776,17848,17911,17985,18049,18180,18244,18511,19439,19525,20588,20749,20845,25445,25526,26676,27478,27564,27681,28431,28505,29378,29472,30222,30320"}}]}]}