{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13978,14062,14162,14228,14298,14363,14438", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "14057,14157,14223,14293,14358,14433,14502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8252,42970", "endColumns": "60,79", "endOffsets": "8308,43045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "27467", "endColumns": "88", "endOffsets": "27551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7073,7679,13135,13426,42358,42798,42888", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "7138,7766,13208,13556,42522,42883,42965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4643,4737,7246,7345,7525,8388,8465,12962,13053,13213,13279,13561,13642,13805,42527,42604,42676", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4732,4817,7340,7441,7609,8460,8553,13048,13130,13274,13342,13637,13719,13872,42599,42671,42793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15498,15737,16000,16153,16245,16313,16817,16884,17022,17084,17154,17229,17289,17351,17426,17549,17622,17861,18783,18851,19865,20017,20095,24275,24378,25494,26254,26306,26441,27123,27192,28002,28101,28823,28916", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "15557,15808,16065,16240,16308,16369,16879,16948,17079,17149,17224,17284,17346,17421,17485,17617,17681,17936,18846,18935,19935,20090,20174,24373,24457,25538,26301,26381,26498,27187,27259,28096,28183,28911,29001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,321", "endColumns": "58,46,159,103", "endOffsets": "109,156,316,420"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "43050,43109,43156,43316", "endColumns": "58,46,159,103", "endOffsets": "43104,43151,43311,43415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15562,16374,16529,16590,16670,16953,17490,17686,17941,18273,18630,18940", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "15633,16435,16585,16665,16727,17017,17544,17757,18009,18336,18690,19005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1025,1118,1193,1258,1346,1411,1477,1535,1606,1672,1726,1836,1896,1960,2014,2087,2203,2287,2368,2501,2586,2671,2804,2894,2968,3020,3071,3137,3214,3296,3380,3454,3528,3607,3684,3756,3863,3952,4028,4119,4214,4288,4361,4455,4509,4583,4655,4741,4827,4889,4953,5016,5087,5188,5291,5386,5486,5542,5597", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "263,339,413,496,585,667,763,871,955,1020,1113,1188,1253,1341,1406,1472,1530,1601,1667,1721,1831,1891,1955,2009,2082,2198,2282,2363,2496,2581,2666,2799,2889,2963,3015,3066,3132,3209,3291,3375,3449,3523,3602,3679,3751,3858,3947,4023,4114,4209,4283,4356,4450,4504,4578,4650,4736,4822,4884,4948,5011,5082,5183,5286,5381,5481,5537,5592,5671"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3217,3293,3367,3450,3539,4355,4451,4559,7614,7850,8313,8558,8623,8711,8776,8842,8900,8971,9037,9091,9201,9261,9325,9379,9452,9568,9652,9733,9866,9951,10036,10169,10259,10333,10385,10436,10502,10579,10661,10745,10819,10893,10972,11049,11121,11228,11317,11393,11484,11579,11653,11726,11820,11874,11948,12020,12106,12192,12254,12318,12381,12452,12553,12656,12751,12851,12907,13347", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,75,73,82,88,81,95,107,83,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,80,132,84,84,132,89,73,51,50,65,76,81,83,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78", "endOffsets": "313,3288,3362,3445,3534,3616,4446,4554,4638,7674,7938,8383,8618,8706,8771,8837,8895,8966,9032,9086,9196,9256,9320,9374,9447,9563,9647,9728,9861,9946,10031,10164,10254,10328,10380,10431,10497,10574,10656,10740,10814,10888,10967,11044,11116,11223,11312,11388,11479,11574,11648,11721,11815,11869,11943,12015,12101,12187,12249,12313,12376,12447,12548,12651,12746,12846,12902,12957,13421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5895", "endColumns": "149", "endOffsets": "6040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3245,3312,3377,3974,4543,4616,4727,4822,4877,4977,5073,5143,5242,5336,5393,5472,5522,5600,5705,5778,5857,5924,5990,6037,6111,6199,6246,6294,6366,6424,6491,6647,6798,6914,6987,7062,7142,7234,7314,7396,7472,7561,7638,7757,7841,7931,7986,8120,8169,8225,8294,8359,8428,8493,8559,8648,8718,8768", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3240,3307,3372,3969,4538,4611,4722,4817,4872,4972,5068,5138,5237,5331,5388,5467,5517,5595,5700,5773,5852,5919,5985,6032,6106,6194,6241,6289,6361,6419,6486,6642,6793,6909,6982,7057,7137,7229,7309,7391,7467,7556,7633,7752,7836,7926,7981,8115,8164,8220,8289,8354,8423,8488,8554,8643,8713,8763,8833"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14507,14578,14658,14720,14793,14875,14936,15221,15273,15351,15424,15638,15813,15912,16070,16440,16732,17762,18014,18110,18180,18341,18431,18519,18695,19010,19103,19180,19280,19353,19443,19534,19618,19697,19797,19940,20179,20289,21036,21103,21168,22711,23280,23353,23464,23559,23614,23714,23810,23880,23979,24073,24130,24698,24748,24826,24931,25004,25083,25150,25637,25997,26071,26159,26206,26503,26575,26633,26700,26856,27007,27319,27392,28323,28403,28570,28650,29006,29082,29245,29832,30394,30478,36544,36599,36733,36882,37442,39294,39359,39428,39493,39559,39648,39718,40991", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "14573,14653,14715,14788,14870,14931,14998,15268,15346,15419,15493,15732,15907,15995,16148,16524,16812,17856,18105,18175,18268,18426,18514,18625,18778,19098,19175,19275,19348,19438,19529,19613,19692,19792,19860,20012,20284,20381,21098,21163,21760,23275,23348,23459,23554,23609,23709,23805,23875,23974,24068,24125,24204,24743,24821,24926,24999,25078,25145,25211,25679,26066,26154,26201,26249,26570,26628,26695,26851,27002,27118,27387,27462,28398,28490,28645,28727,29077,29166,29317,29946,30473,30563,36594,36728,36777,36933,37506,39354,39423,39488,39554,39643,39713,39763,41056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,194,329,402,473,562,621,685,779,894,1048,1296,1565,1644,1718,1794,1885,2001,2128,2193,2271,2413,2479,2536,2604,2685,2816,2936,3043,3130,3210,3295,3364", "endColumns": "68,69,134,72,70,88,58,63,93,114,153,247,268,78,73,75,90,115,126,64,77,141,65,56,67,80,130,119,106,86,79,84,68,146", "endOffsets": "119,189,324,397,468,557,616,680,774,889,1043,1291,1560,1639,1713,1789,1880,1996,2123,2188,2266,2408,2474,2531,2599,2680,2811,2931,3038,3125,3205,3290,3359,3506"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15003,15151,28188,29951,30323,36455,38216,38275,38339,38433,38548,38702,38950,40285,40364,40438,40514,40605,40721,40848,40913,41118,41260,41326,41383,41451,41532,41663,41783,41890,41977,42057,42142,42211", "endColumns": "68,69,134,72,70,88,58,63,93,114,153,247,268,78,73,75,90,115,126,64,77,141,65,56,67,80,130,119,106,86,79,84,68,146", "endOffsets": "15067,15216,28318,30019,30389,36539,38270,38334,38428,38543,38697,38945,39214,40359,40433,40509,40600,40716,40843,40908,40986,41255,41321,41378,41446,41527,41658,41778,41885,41972,42052,42137,42206,42353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,336,425,611,660,726,823,893,1134,1204,1303,1369,1425,1489,1767,1835,1898,1953,2008,2082,2200,2308,2367,2454,2529,2620,2694,2794,3048,3127,3204,3272,3332,3394,3456,3526,3605,3703,3805,3900,4000,4093,4168,4247,4332,4516,4711,4827,4960,5020,5663,5725", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,61,58", "endOffsets": "171,331,420,606,655,721,818,888,1129,1199,1298,1364,1420,1484,1762,1830,1893,1948,2003,2077,2195,2303,2362,2449,2524,2615,2689,2789,3043,3122,3199,3267,3327,3389,3451,3521,3600,3698,3800,3895,3995,4088,4163,4242,4327,4511,4706,4822,4955,5015,5658,5720,5779"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20386,20507,20667,20850,21765,21814,21880,21977,22047,22288,22358,24209,24462,24634,25216,25866,25934,26386,27264,27556,27630,27748,27856,27915,28495,28732,29171,29322,29422,29676,29755,30024,30201,30261,30568,31135,31205,31372,31561,32567,34416,34516,34609,34684,34763,36271,36938,37133,37249,37382,37511,38154,39768", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,61,58", "endOffsets": "20502,20662,20751,21031,21809,21875,21972,22042,22283,22353,22452,24270,24513,24693,25489,25929,25992,26436,27314,27625,27743,27851,27910,27997,28565,28818,29240,29417,29671,29750,29827,30087,30256,30318,30625,31200,31279,31465,31658,32657,34511,34604,34679,34758,34843,36450,37128,37244,37377,37437,38149,38211,39822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4902,5013,5166,5297,5403,5546,5672,5788,6045,6186,6292,6441,6567,6715,6854,6920,6990", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "5008,5161,5292,5398,5541,5667,5783,5890,6181,6287,6436,6562,6710,6849,6915,6985,7068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3621,3717,3819,3917,4022,4127,4239,13877", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3712,3814,3912,4017,4122,4234,4350,13973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4822,7446,7771", "endColumns": "79,78,78", "endOffsets": "4897,7520,7845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7143,7943,8044,8153", "endColumns": "102,100,108,98", "endOffsets": "7241,8039,8148,8247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,13724", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,13800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,228,325,404,482,598,692,781,874,983,1148,1341,1488,1576,1667,1762,1857,2049,2149,2246,2478,2571,2751,2839,2899,2964,3042,3140,3232,3331,3536,3605,3674,3744,3826,3910,3975,4055,4126,4201,4325,4414,4522,4614,4688,4769,4837,4918,4980,5078,5191,5296,5427,5493,5581,5674,5748,5848,5923,6011,6079,6182,6295,6381", "endColumns": "78,93,96,78,77,115,93,88,92,108,164,192,146,87,90,94,94,191,99,96,231,92,179,87,59,64,77,97,91,98,204,68,68,69,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,73,99,74,87,67,102,112,85,56", "endOffsets": "129,223,320,399,477,593,687,776,869,978,1143,1336,1483,1571,1662,1757,1852,2044,2144,2241,2473,2566,2746,2834,2894,2959,3037,3135,3227,3326,3531,3600,3669,3739,3821,3905,3970,4050,4121,4196,4320,4409,4517,4609,4683,4764,4832,4913,4975,5073,5186,5291,5422,5488,5576,5669,5743,5843,5918,6006,6074,6177,6290,6376,6433"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15072,20756,22457,22554,22633,24518,25543,25684,25773,30092,30630,30795,30988,31284,31470,31663,31758,31853,32045,32145,32242,32474,32662,32842,32930,32990,33055,33133,33231,33323,33422,33627,33696,33765,33835,33917,34001,34066,34146,34217,34292,34848,34937,35045,35137,35211,35292,35360,35441,35503,35601,35714,35819,35950,36016,36104,36197,36782,39219,39827,39915,39983,40086,40199,41061", "endColumns": "78,93,96,78,77,115,93,88,92,108,164,192,146,87,90,94,94,191,99,96,231,92,179,87,59,64,77,97,91,98,204,68,68,69,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,73,99,74,87,67,102,112,85,56", "endOffsets": "15146,20845,22549,22628,22706,24629,25632,25768,25861,30196,30790,30983,31130,31367,31556,31753,31848,32040,32140,32237,32469,32562,32837,32925,32985,33050,33128,33226,33318,33417,33622,33691,33760,33830,33912,33996,34061,34141,34212,34287,34411,34932,35040,35132,35206,35287,35355,35436,35498,35596,35709,35814,35945,36011,36099,36192,36266,36877,39289,39910,39978,40081,40194,40280,41113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2988,3100", "endColumns": "111,116", "endOffsets": "3095,3212"}}]}]}