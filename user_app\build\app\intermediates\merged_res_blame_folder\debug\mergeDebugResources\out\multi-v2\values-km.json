{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4645,4730,7276,7380,7550,8426,8510,13096,13181,13346,13411,13692,13772,13941,14373,14457,14523", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "4725,4805,7375,7473,7633,8505,8588,13176,13263,13406,13471,13767,13852,14012,14452,14518,14636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3632,3727,3830,3928,4028,4129,4241,14017", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3722,3825,3923,4023,4124,4236,4348,14113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,206", "endColumns": "78,71,77", "endOffsets": "129,201,279"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4810,7478,7793", "endColumns": "78,71,77", "endOffsets": "4884,7545,7866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "85", "endOffsets": "136"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "14118", "endColumns": "85", "endOffsets": "14199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,13857", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,13936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4889,4991,5146,5267,5372,5534,5658,5779,6037,6195,6312,6483,6608,6753,6911,6975,7033", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "4986,5141,5262,5367,5529,5653,5774,5883,6190,6307,6478,6603,6748,6906,6970,7028,7105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5888", "endColumns": "148", "endOffsets": "6032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2795,2892,2970,3025,3081,3147,3227,3317,3403,3488,3567,3644,3714,3789,3901,3989,4062,4162,4261,4335,4411,4518,4572,4662,4735,4826,4922,4984,5048,5111,5182,5281,5379,5471,5567,5625,5685", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2790,2887,2965,3020,3076,3142,3222,3312,3398,3483,3562,3639,3709,3784,3896,3984,4057,4157,4256,4330,4406,4513,4567,4657,4730,4821,4917,4979,5043,5106,5177,5276,5374,5466,5562,5620,5680,5763"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3240,3318,3394,3474,3553,4353,4453,4565,7638,7871,8356,8593,8655,8742,8805,8870,8929,8994,9055,9112,9231,9289,9350,9407,9478,9608,9694,9772,9910,9985,10056,10206,10303,10381,10436,10492,10558,10638,10728,10814,10899,10978,11055,11125,11200,11312,11400,11473,11573,11672,11746,11822,11929,11983,12073,12146,12237,12333,12395,12459,12522,12593,12692,12790,12882,12978,13036,13476", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,149,96,77,54,55,65,79,89,85,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82", "endOffsets": "314,3313,3389,3469,3548,3627,4448,4560,4640,7698,7960,8421,8650,8737,8800,8865,8924,8989,9050,9107,9226,9284,9345,9402,9473,9603,9689,9767,9905,9980,10051,10201,10298,10376,10431,10487,10553,10633,10723,10809,10894,10973,11050,11120,11195,11307,11395,11468,11568,11667,11741,11817,11924,11978,12068,12141,12232,12328,12390,12454,12517,12588,12687,12785,12877,12973,13031,13091,13554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,116", "endOffsets": "166,283"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3007,3123", "endColumns": "115,116", "endOffsets": "3118,3235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,72", "endOffsets": "258,331"}, "to": {"startLines": "84,163", "startColumns": "4,4", "startOffsets": "8295,14798", "endColumns": "60,76", "endOffsets": "8351,14870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,264,342,475,644,724", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "169,259,337,470,639,719,796"}, "to": {"startLines": "71,78,146,150,157,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7110,7703,13268,13559,14204,14641,14721", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "7174,7788,13341,13687,14368,14716,14793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7179,7965,8062,8195", "endColumns": "96,96,132,99", "endOffsets": "7271,8057,8190,8290"}}]}]}