{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-nn-rNO/values-nn-rNO.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,254,339,401,471,530,606,678,743,803", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "128,187,249,334,396,466,525,601,673,738,798,867"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1653,2447,2583,2645,2730,3006,3534,3726,3971,4289,4621,4912", "endColumns": "77,58,61,84,61,69,58,75,71,64,59,68", "endOffsets": "1726,2501,2640,2725,2787,3071,3588,3797,4038,4349,4676,4976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,209,331,390,454,539,593,651,725,825,984,1256,1550,1624,1692,1779,1870,1976,2100,2168,2241,2409,2478,2537,2611,2693,2827,2955,3069,3154,3229,3319,3387", "endColumns": "70,82,121,58,63,84,53,57,73,99,158,271,293,73,67,86,90,105,123,67,72,167,68,58,73,81,133,127,113,84,74,89,67,155", "endOffsets": "121,204,326,385,449,534,588,646,720,820,979,1251,1545,1619,1687,1774,1865,1971,2095,2163,2236,2404,2473,2532,2606,2688,2822,2950,3064,3149,3224,3314,3382,3538"}, "to": {"startLines": "16,18,162,180,185,247,260,261,262,263,264,265,266,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,299,300,301,302,303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1056,1211,14373,16124,16482,22621,24381,24435,24493,24567,24667,24826,25098,26452,26526,26594,26681,26772,26878,27002,27070,27275,27443,27512,27571,27645,27727,27861,27989,28103,28188,28263,28353,28421", "endColumns": "70,82,121,58,63,84,53,57,73,99,158,271,293,73,67,86,90,105,123,67,72,167,68,58,73,81,133,127,113,84,74,89,67,155", "endOffsets": "1122,1289,14490,16178,16541,22701,24430,24488,24562,24662,24821,25093,25387,26521,26589,26676,26767,26873,26997,27065,27138,27438,27507,27566,27640,27722,27856,27984,28098,28183,28258,28348,28416,28572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,311,378,442,514", "endColumns": "86,101,66,66,63,71,66", "endOffsets": "137,239,306,373,437,509,576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,467,530,583,661,743,824,920,1016,1099,1181,1258,1338,1432,1522,1591,1678,1761,1844,1945,2023,2110,2186,2280,2359,2455,2548,2637,2721,2817,2890,2969,3073,3170,3240,3305,3988,4647,4721,4839,4934,4994,5097,5192,5259,5349,5437,5494,5572,5621,5697,5793,5861,5932,5999,6065,6111,6188,6280,6327,6375,6442,6500,6560,6725,6890,7008,7074,7157,7232,7332,7417,7504,7582,7670,7747,7850,7935,8020,8073,8205,8253,8306,8375,8441,8511,8575,8647,8728,8795,8846", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "118,195,257,325,402,462,525,578,656,738,819,915,1011,1094,1176,1253,1333,1427,1517,1586,1673,1756,1839,1940,2018,2105,2181,2275,2354,2450,2543,2632,2716,2812,2885,2964,3068,3165,3235,3300,3983,4642,4716,4834,4929,4989,5092,5187,5254,5344,5432,5489,5567,5616,5692,5788,5856,5927,5994,6060,6106,6183,6275,6322,6370,6437,6495,6555,6720,6885,7003,7069,7152,7227,7327,7412,7499,7577,7665,7742,7845,7930,8015,8068,8200,8248,8301,8370,8436,8506,8570,8642,8723,8790,8841,8919"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,124,125,126,127,131,136,137,138,139,144,145,146,147,148,149,153,154,163,164,166,167,171,172,174,179,186,187,248,249,250,252,257,268,269,270,271,272,273,274,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "581,649,726,788,856,933,993,1294,1347,1425,1507,1731,1905,2001,2151,2506,2792,3802,4043,4133,4202,4354,4437,4520,4681,4981,5068,5144,5238,5317,5413,5506,5595,5679,5775,5922,6177,6281,7052,7122,7187,8805,9464,9538,9656,9751,9811,9914,10009,10076,10166,10254,10311,10889,10938,11014,11110,11178,11249,11316,11840,12220,12297,12389,12436,12730,12797,12855,12915,13080,13245,13557,13623,14495,14570,14746,14831,15186,15264,15417,16021,16546,16631,22706,22759,22891,23038,23578,25464,25530,25600,25664,25736,25817,25884,27143", "endColumns": "67,76,61,67,76,59,62,52,77,81,80,95,95,82,81,76,79,93,89,68,86,82,82,100,77,86,75,93,78,95,92,88,83,95,72,78,103,96,69,64,682,658,73,117,94,59,102,94,66,89,87,56,77,48,75,95,67,70,66,65,45,76,91,46,47,66,57,59,164,164,117,65,82,74,99,84,86,77,87,76,102,84,84,52,131,47,52,68,65,69,63,71,80,66,50,77", "endOffsets": "644,721,783,851,928,988,1051,1342,1420,1502,1583,1822,1996,2079,2228,2578,2867,3891,4128,4197,4284,4432,4515,4616,4754,5063,5139,5233,5312,5408,5501,5590,5674,5770,5843,5996,6276,6373,7117,7182,7865,9459,9533,9651,9746,9806,9909,10004,10071,10161,10249,10306,10384,10933,11009,11105,11173,11244,11311,11377,11881,12292,12384,12431,12479,12792,12850,12910,13075,13240,13358,13618,13701,14565,14665,14826,14913,15259,15347,15489,16119,16626,16711,22754,22886,22934,23086,23642,25525,25595,25659,25731,25812,25879,25930,27216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,233,319,396,473,592,686,790,879,995,1171,1361,1503,1594,1703,1796,1893,2069,2172,2268,2488,2593,2778,2870,2929,2994,3082,3179,3275,3380,3576,3645,3712,3784,3869,3959,4025,4107,4178,4250,4369,4455,4556,4646,4717,4796,4863,4947,5006,5107,5217,5322,5452,5512,5602,5695,5767,5866,5938,6024,6096,6204,6314,6400", "endColumns": "83,93,85,76,76,118,93,103,88,115,175,189,141,90,108,92,96,175,102,95,219,104,184,91,58,64,87,96,95,104,195,68,66,71,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,71,98,71,85,71,107,109,85,53", "endOffsets": "134,228,314,391,468,587,681,785,874,990,1166,1356,1498,1589,1698,1791,1888,2064,2167,2263,2483,2588,2773,2865,2924,2989,3077,3174,3270,3375,3571,3640,3707,3779,3864,3954,4020,4102,4173,4245,4364,4450,4551,4641,4712,4791,4858,4942,5001,5102,5212,5317,5447,5507,5597,5690,5762,5861,5933,6019,6091,6199,6309,6395,6449"}, "to": {"startLines": "17,88,100,101,102,119,130,132,133,182,189,190,191,194,196,198,199,200,201,202,203,204,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,251,267,276,277,278,279,280,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1127,6745,8565,8651,8728,10706,11746,11886,11990,16244,16776,16952,17142,17435,17626,17828,17921,18018,18194,18297,18393,18613,18811,18996,19088,19147,19212,19300,19397,19493,19598,19794,19863,19930,20002,20087,20177,20243,20325,20396,20468,21022,21108,21209,21299,21370,21449,21516,21600,21659,21760,21870,21975,22105,22165,22255,22348,22939,25392,25990,26076,26148,26256,26366,27221", "endColumns": "83,93,85,76,76,118,93,103,88,115,175,189,141,90,108,92,96,175,102,95,219,104,184,91,58,64,87,96,95,104,195,68,66,71,84,89,65,81,70,71,118,85,100,89,70,78,66,83,58,100,109,104,129,59,89,92,71,98,71,85,71,107,109,85,53", "endOffsets": "1206,6834,8646,8723,8800,10820,11835,11985,12074,16355,16947,17137,17279,17521,17730,17916,18013,18189,18292,18388,18608,18713,18991,19083,19142,19207,19295,19392,19488,19593,19789,19858,19925,19997,20082,20172,20238,20320,20391,20463,20582,21103,21204,21294,21365,21444,21511,21595,21654,21755,21865,21970,22100,22160,22250,22343,22415,23033,25459,26071,26143,26251,26361,26447,27270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,351,418,479,546,613,677,745,815,875,934,1007,1071,1141,1204,1279,1343,1432,1506,1591,1682,1790,1878,1926,1974,2055,2119,2191,2260,2359,2445,2537", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "115,193,260,346,413,474,541,608,672,740,810,870,929,1002,1066,1136,1199,1274,1338,1427,1501,1586,1677,1785,1873,1921,1969,2050,2114,2186,2255,2354,2440,2532,2625"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,129,140,141,143,150,151,160,161,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1588,1827,2084,2233,2319,2386,2872,2939,3076,3140,3208,3278,3338,3397,3470,3593,3663,3896,4759,4823,5848,6001,6086,10455,10563,11698,12484,12532,12666,13363,13435,14188,14287,15001,15093", "endColumns": "64,77,66,85,66,60,66,66,63,67,69,59,58,72,63,69,62,74,63,88,73,84,90,107,87,47,47,80,63,71,68,98,85,91,92", "endOffsets": "1648,1900,2146,2314,2381,2442,2934,3001,3135,3203,3273,3333,3392,3465,3529,3658,3721,3966,4818,4907,5917,6081,6172,10558,10646,11741,12527,12608,12725,13430,13499,14282,14368,15088,15181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-nn-rNO\\values-nn-rNO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,326,422,635,683,750,852,921,1171,1236,1330,1396,1451,1515,1831,1906,1972,2025,2078,2173,2305,2419,2476,2560,2636,2719,2784,2884,3153,3231,3311,3372,3432,3494,3554,3625,3705,3805,3898,3991,4089,4186,4259,4338,4426,4627,4822,4936,5058,5114,5786,5848", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,61,54", "endOffsets": "164,321,417,630,678,745,847,916,1166,1231,1325,1391,1446,1510,1826,1901,1967,2020,2073,2168,2300,2414,2471,2555,2631,2714,2779,2879,3148,3226,3306,3367,3427,3489,3549,3620,3700,3800,3893,3986,4084,4181,4254,4333,4421,4622,4817,4931,5053,5109,5781,5843,5898"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,128,134,135,142,152,155,156,157,158,159,165,168,173,175,176,177,178,181,183,184,188,192,193,195,197,205,225,226,227,228,229,246,253,254,255,256,258,259,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6378,6492,6649,6839,7870,7918,7985,8087,8156,8406,8471,10389,10651,10825,11382,12079,12154,12613,13504,13706,13801,13933,14047,14104,14670,14918,15352,15494,15594,15863,15941,16183,16360,16420,16716,17284,17355,17526,17735,18718,20587,20685,20782,20855,20934,22420,23091,23286,23400,23522,23647,24319,25935", "endColumns": "113,156,95,212,47,66,101,68,249,64,93,65,54,63,315,74,65,52,52,94,131,113,56,83,75,82,64,99,268,77,79,60,59,61,59,70,79,99,92,92,97,96,72,78,87,200,194,113,121,55,671,61,54", "endOffsets": "6487,6644,6740,7047,7913,7980,8082,8151,8401,8466,8560,10450,10701,10884,11693,12149,12215,12661,13552,13796,13928,14042,14099,14183,14741,14996,15412,15589,15858,15936,16016,16239,16415,16477,16771,17350,17430,17621,17823,18806,20680,20777,20850,20929,21017,22616,23281,23395,23517,23573,24314,24376,25985"}}]}]}