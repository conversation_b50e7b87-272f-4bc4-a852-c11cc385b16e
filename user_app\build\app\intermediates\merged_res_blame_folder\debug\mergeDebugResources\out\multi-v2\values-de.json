{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7370,7973,13545,13839,44951,45385,45465", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "7437,8056,13623,13974,45115,45460,45536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f1b710ea72e98967c6072cb046dd43c\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14402,14492,14594,14662,14729,14796,14876", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "14487,14589,14657,14724,14791,14871,14945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8544,45541", "endColumns": "60,77", "endOffsets": "8600,45614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,14144", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,14221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4941,7744,8061", "endColumns": "77,75,76", "endOffsets": "5014,7815,8133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,245,335,413,490,636,736,848,947,1093,1285,1493,1666,1759,1865,1962,2058,2254,2360,2456,2714,2830,3042,3139,3203,3270,3361,3457,3555,3658,3902,3974,4042,4118,4195,4284,4369,4458,4530,4599,4724,4817,4921,5015,5091,5174,5245,5335,5398,5503,5621,5729,5859,5925,6021,6122,6194,6301,6387,6471,6544,6661,6798,6889", "endColumns": "89,99,89,77,76,145,99,111,98,145,191,207,172,92,105,96,95,195,105,95,257,115,211,96,63,66,90,95,97,102,243,71,67,75,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,71,106,85,83,72,116,136,90,58", "endOffsets": "140,240,330,408,485,631,731,843,942,1088,1280,1488,1661,1754,1860,1957,2053,2249,2355,2451,2709,2825,3037,3134,3198,3265,3356,3452,3550,3653,3897,3969,4037,4113,4190,4279,4364,4453,4525,4594,4719,4812,4916,5010,5086,5169,5240,5330,5393,5498,5616,5724,5854,5920,6016,6117,6189,6296,6382,6466,6539,6656,6793,6884,6943"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15509,21360,23301,23391,23469,25542,26640,26791,26903,31571,32144,32336,32544,32874,33063,33268,33365,33461,33657,33763,33859,34117,34316,34528,34625,34689,34756,34847,34943,35041,35144,35388,35460,35528,35604,35681,35770,35855,35944,36016,36085,36660,36753,36857,36951,37027,37110,37181,37271,37334,37439,37557,37665,37795,37861,37957,38058,38714,41532,42171,42255,42328,42445,42582,43512", "endColumns": "89,99,89,77,76,145,99,111,98,145,191,207,172,92,105,96,95,195,105,95,257,115,211,96,63,66,90,95,97,102,243,71,67,75,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,71,106,85,83,72,116,136,90,58", "endOffsets": "15594,21455,23386,23464,23541,25683,26735,26898,26997,31712,32331,32539,32712,32962,33164,33360,33456,33652,33758,33854,34112,34228,34523,34620,34684,34751,34842,34938,35036,35139,35383,35455,35523,35599,35676,35765,35850,35939,36011,36080,36205,36748,36852,36946,37022,37105,37176,37266,37329,37434,37552,37660,37790,37856,37952,38053,38125,38816,41613,42250,42323,42440,42577,42668,43566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3351,3436,3501,4206,4899,4973,5103,5209,5264,5372,5472,5540,5634,5734,5791,5879,5931,6011,6114,6191,6263,6330,6396,6447,6528,6623,6670,6721,6796,6854,6915,7135,7335,7494,7559,7648,7738,7834,7920,8007,8084,8173,8252,8365,8452,8542,8598,8743,8795,8850,8919,8988,9063,9127,9199,9288,9361,9417", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3346,3431,3496,4201,4894,4968,5098,5204,5259,5367,5467,5535,5629,5729,5786,5874,5926,6006,6109,6186,6258,6325,6391,6442,6523,6618,6665,6716,6791,6849,6910,7130,7330,7489,7554,7643,7733,7829,7915,8002,8079,8168,8247,8360,8447,8537,8593,8738,8790,8845,8914,8983,9058,9122,9194,9283,9356,9412,9488"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14950,15020,15099,15168,15237,15315,15377,15680,15736,15823,15905,16135,16309,16406,16559,16921,17214,18249,18493,18587,18658,18824,18909,18998,19177,19482,19573,19651,19749,19838,19942,20031,20122,20216,20341,20503,20778,20880,21711,21796,21861,23546,24239,24313,24443,24549,24604,24712,24812,24880,24974,25074,25131,25752,25804,25884,25987,26064,26136,26203,26740,27142,27223,27318,27365,27665,27740,27798,27859,28079,28279,28635,28700,29685,29775,29952,30038,30403,30480,30636,31334,31907,31994,38461,38517,38662,38821,39430,41618,41687,41762,41826,41898,41987,42060,43436", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "15015,15094,15163,15232,15310,15372,15431,15731,15818,15900,15988,16227,16401,16487,16643,16994,17298,18340,18582,18653,18744,18904,18993,19106,19258,19568,19646,19744,19833,19937,20026,20117,20211,20336,20421,20593,20875,20970,21791,21856,22561,24234,24308,24438,24544,24599,24707,24807,24875,24969,25069,25126,25214,25799,25879,25982,26059,26131,26198,26264,26786,27218,27313,27360,27411,27735,27793,27854,28074,28274,28433,28695,28784,29770,29866,30033,30120,30475,30564,30710,31442,31989,32079,38512,38657,38709,38871,39494,41682,41757,41821,41893,41982,42055,42111,43507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5019,5128,5292,5420,5532,5710,5841,5962,6226,6406,6518,6687,6818,6980,7156,7227,7290", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "5123,5287,5415,5527,5705,5836,5957,6076,6401,6513,6682,6813,6975,7151,7222,7285,7365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4757,4853,7546,7644,7820,8675,8760,13368,13457,13628,13693,13979,14060,14226,45120,45199,45265", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4848,4936,7639,7739,7902,8755,8847,13452,13540,13688,13752,14055,14139,14296,45194,45260,45380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15993,16232,16492,16648,16733,16802,17303,17375,17520,17584,17652,17722,17782,17844,17917,18040,18110,18345,19263,19326,20426,20598,20685,25287,25399,26591,27416,27464,27603,28438,28513,29365,29464,30211,30309", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "16053,16304,16554,16728,16797,16858,17370,17437,17579,17647,17717,17777,17839,17912,17976,18105,18168,18414,19321,19406,20498,20680,20773,25394,25482,26635,27459,27545,27660,28508,28577,29459,29547,30304,30398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f67f4b43e690c05ab4f64782d5e6805b\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,114,161,333", "endColumns": "58,46,171,108", "endOffsets": "109,156,328,437"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "45619,45678,45725,45897", "endColumns": "58,46,171,108", "endOffsets": "45673,45720,45892,46001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6081", "endColumns": "144", "endOffsets": "6221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,337,440,691,738,805,906,975,1265,1328,1426,1494,1549,1613,1935,2011,2075,2128,2181,2272,2403,2519,2576,2665,2746,2832,2899,3005,3361,3441,3518,3581,3641,3704,3764,3838,3921,4017,4116,4199,4305,4405,4479,4558,4649,4888,5136,5253,5383,5442,6248,6313", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,64,54", "endOffsets": "170,332,435,686,733,800,901,970,1260,1323,1421,1489,1544,1608,1930,2006,2070,2123,2176,2267,2398,2514,2571,2660,2741,2827,2894,3000,3356,3436,3513,3576,3636,3699,3759,3833,3916,4012,4111,4194,4300,4400,4474,4553,4644,4883,5131,5248,5378,5437,6243,6308,6363"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20975,21095,21257,21460,22566,22613,22680,22781,22850,23140,23203,25219,25487,25688,26269,27002,27078,27550,28582,28881,28972,29103,29219,29276,29871,30125,30569,30715,30821,31177,31257,31508,31717,31777,32084,32717,32791,32967,33169,34233,36210,36316,36416,36490,36569,38130,38876,39124,39241,39371,39499,40305,42116", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,64,54", "endOffsets": "21090,21252,21355,21706,22608,22675,22776,22845,23135,23198,23296,25282,25537,25747,26586,27073,27137,27598,28630,28967,29098,29214,29271,29360,29947,30206,30631,30816,31172,31252,31329,31566,31772,31835,32139,32786,32869,33058,33263,34311,36311,36411,36485,36564,36655,38364,39119,39236,39366,39425,40300,40365,42166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,209,342,403,470,562,621,686,772,881,1071,1389,1724,1799,1877,1960,2052,2193,2345,2413,2487,2671,2739,2798,2872,2960,3105,3244,3368,3460,3540,3626,3700", "endColumns": "72,80,132,60,66,91,58,64,85,108,189,317,334,74,77,82,91,140,151,67,73,183,67,58,73,87,144,138,123,91,79,85,73,166", "endOffsets": "123,204,337,398,465,557,616,681,767,876,1066,1384,1719,1794,1872,1955,2047,2188,2340,2408,2482,2666,2734,2793,2867,2955,3100,3239,3363,3455,3535,3621,3695,3862"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15436,15599,29552,31447,31840,38369,40370,40429,40494,40580,40689,40879,41197,42673,42748,42826,42909,43001,43142,43294,43362,43571,43755,43823,43882,43956,44044,44189,44328,44452,44544,44624,44710,44784", "endColumns": "72,80,132,60,66,91,58,64,85,108,189,317,334,74,77,82,91,140,151,67,73,183,67,58,73,87,144,138,123,91,79,85,73,166", "endOffsets": "15504,15675,29680,31503,31902,38456,40424,40489,40575,40684,40874,41192,41527,42743,42821,42904,42996,43137,43289,43357,43431,43750,43818,43877,43951,44039,44184,44323,44447,44539,44619,44705,44779,44946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7442,8232,8333,8444", "endColumns": "103,100,110,99", "endOffsets": "7541,8328,8439,8539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3060,3172", "endColumns": "111,113", "endOffsets": "3167,3281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3722,3820,3922,4022,4122,4230,4335,14301", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3815,3917,4017,4117,4225,4330,4448,14397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16058,16863,16999,17061,17145,17442,17981,18173,18419,18749,19111,19411", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "16130,16916,17056,17140,17209,17515,18035,18244,18488,18819,19172,19477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2946,3036,3115,3172,3223,3289,3363,3445,3538,3613,3687,3765,3837,3911,4021,4113,4195,4284,4373,4447,4525,4611,4666,4745,4812,4892,4976,5038,5102,5165,5234,5341,5448,5547,5653,5714,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2941,3031,3110,3167,3218,3284,3358,3440,3533,3608,3682,3760,3832,3906,4016,4108,4190,4279,4368,4442,4520,4606,4661,4740,4807,4887,4971,5033,5097,5160,5229,5336,5443,5542,5648,5709,5764,5846"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3286,3377,3466,3550,3640,4453,4554,4676,7907,8138,8605,8852,8911,9019,9085,9154,9212,9284,9348,9402,9530,9590,9652,9706,9784,9921,10013,10097,10242,10326,10412,10545,10635,10714,10771,10822,10888,10962,11044,11137,11212,11286,11364,11436,11510,11620,11712,11794,11883,11972,12046,12124,12210,12265,12344,12411,12491,12575,12637,12701,12764,12833,12940,13047,13146,13252,13313,13757", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,132,89,78,56,50,65,73,81,92,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81", "endOffsets": "328,3372,3461,3545,3635,3717,4549,4671,4752,7968,8227,8670,8906,9014,9080,9149,9207,9279,9343,9397,9525,9585,9647,9701,9779,9916,10008,10092,10237,10321,10407,10540,10630,10709,10766,10817,10883,10957,11039,11132,11207,11281,11359,11431,11505,11615,11707,11789,11878,11967,12041,12119,12205,12260,12339,12406,12486,12570,12632,12696,12759,12828,12935,13042,13141,13247,13308,13363,13834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "28789", "endColumns": "91", "endOffsets": "28876"}}]}]}