{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7139,7926,8027,8139", "endColumns": "109,100,111,96", "endOffsets": "7244,8022,8134,8231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2769,2857,2935,2989,3040,3106,3174,3248,3338,3414,3485,3563,3633,3703,3803,3892,3970,4058,4148,4220,4292,4376,4427,4505,4571,4652,4735,4797,4861,4924,4993,5093,5197,5290,5390,5448,5503", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2764,2852,2930,2984,3035,3101,3169,3243,3333,3409,3480,3558,3628,3698,3798,3887,3965,4053,4143,4215,4287,4371,4422,4500,4566,4647,4730,4792,4856,4919,4988,5088,5192,5285,5385,5443,5498,5576"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3175,3252,3325,3412,3500,4306,4405,4524,7610,7834,8297,8529,8589,8676,8740,8802,8866,8934,8999,9053,9162,9220,9282,9336,9411,9531,9613,9693,9827,9905,9985,10108,10196,10274,10328,10379,10445,10513,10587,10677,10753,10824,10902,10972,11042,11142,11231,11309,11397,11487,11559,11631,11715,11766,11844,11910,11991,12074,12136,12200,12263,12332,12432,12536,12629,12729,12787,13219", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,122,87,77,53,50,65,67,73,89,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77", "endOffsets": "306,3247,3320,3407,3495,3575,4400,4519,4601,7669,7921,8360,8584,8671,8735,8797,8861,8929,8994,9048,9157,9215,9277,9331,9406,9526,9608,9688,9822,9900,9980,10103,10191,10269,10323,10374,10440,10508,10582,10672,10748,10819,10897,10967,11037,11137,11226,11304,11392,11482,11554,11626,11710,11761,11839,11905,11986,12069,12131,12195,12258,12327,12427,12531,12624,12724,12782,12837,13292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "84,457", "startColumns": "4,4", "startOffsets": "8236,42399", "endColumns": "60,74", "endOffsets": "8292,42469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3580,3674,3776,3873,3972,4080,4186,13751", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3669,3771,3868,3967,4075,4181,4301,13847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5864", "endColumns": "129", "endOffsets": "5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "171,181,183,184,185,189,197,200,203,207,211,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14912,15711,15846,15908,15993,16274,16804,16996,17244,17568,17918,18212", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "14985,15763,15903,15988,16050,16339,16858,17067,17311,17629,17973,18276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,322,417,614,662,729,826,895,1124,1189,1287,1353,1408,1472,1767,1841,1907,1960,2013,2105,2231,2338,2395,2479,2555,2638,2703,2802,3078,3155,3232,3293,3353,3415,3475,3546,3626,3726,3819,3900,4003,4100,4173,4252,4337,4514,4706,4824,4953,5009,5669,5734", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,64,54", "endOffsets": "163,317,412,609,657,724,821,890,1119,1184,1282,1348,1403,1467,1762,1836,1902,1955,2008,2100,2226,2333,2390,2474,2550,2633,2698,2797,3073,3150,3227,3288,3348,3410,3470,3541,3621,3721,3814,3895,3998,4095,4168,4247,4332,4509,4701,4819,4948,5004,5664,5729,5784"}, "to": {"startLines": "232,233,234,236,240,241,242,243,244,245,246,262,265,267,275,281,282,289,299,302,303,304,305,306,312,315,320,322,323,324,325,328,330,331,335,339,340,342,344,352,372,373,374,375,376,393,400,401,402,403,405,406,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19681,19794,19948,20136,21147,21195,21262,21359,21428,21657,21722,23608,23859,24035,24611,25281,25355,25812,26721,26925,27017,27143,27250,27307,27866,28104,28521,28663,28762,29038,29115,29353,29535,29595,29890,30455,30526,30697,30900,31897,33786,33889,33986,34059,34138,35642,36294,36486,36604,36733,36858,37518,39143", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,64,54", "endOffsets": "19789,19943,20038,20328,21190,21257,21354,21423,21652,21717,21815,23669,23909,24094,24901,25350,25416,25860,26769,27012,27138,27245,27302,27386,27937,28182,28581,28757,29033,29110,29187,29409,29590,29652,29945,30521,30601,30792,30988,31973,33884,33981,34054,34133,34218,35814,36481,36599,36728,36784,37513,37578,39193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3200,3272,3337,4014,4666,4740,4855,4950,5005,5098,5183,5250,5338,5426,5483,5561,5610,5687,5793,5865,5940,6007,6073,6119,6198,6290,6337,6385,6456,6514,6574,6754,6916,7040,7107,7191,7269,7366,7446,7528,7601,7690,7767,7869,7953,8038,8091,8223,8271,8325,8394,8460,8529,8593,8663,8751,8818,8869", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3195,3267,3332,4009,4661,4735,4850,4945,5000,5093,5178,5245,5333,5421,5478,5556,5605,5682,5788,5860,5935,6002,6068,6114,6193,6285,6332,6380,6451,6509,6569,6749,6911,7035,7102,7186,7264,7361,7441,7523,7596,7685,7762,7864,7948,8033,8086,8218,8266,8320,8389,8455,8524,8588,8658,8746,8813,8864,8944"}, "to": {"startLines": "156,157,158,159,160,161,162,166,167,168,169,172,174,175,177,182,186,201,204,205,206,208,209,210,212,216,217,218,219,220,221,222,223,224,225,227,230,231,237,238,239,250,251,252,253,254,255,256,257,258,259,260,261,268,269,270,271,272,273,274,278,283,284,285,286,291,292,293,294,295,296,300,301,310,311,313,314,318,319,321,326,333,334,395,396,397,399,404,415,416,417,418,419,420,421,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13852,13920,13997,14059,14127,14204,14262,14560,14613,14689,14766,14990,15167,15266,15419,15768,16055,17072,17316,17409,17478,17634,17721,17809,17978,18281,18371,18447,18544,18623,18719,18807,18896,18981,19082,19231,19480,19584,20333,20405,20470,22061,22713,22787,22902,22997,23052,23145,23230,23297,23385,23473,23530,24099,24148,24225,24331,24403,24478,24545,25047,25421,25500,25592,25639,25929,26000,26058,26118,26298,26460,26774,26841,27691,27769,27942,28022,28359,28432,28586,29192,29721,29805,35906,35959,36091,36240,36789,38668,38734,38803,38867,38937,39025,39092,40379", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "13915,13992,14054,14122,14199,14257,14316,14608,14684,14761,14842,15084,15261,15347,15494,15841,16133,17164,17404,17473,17563,17716,17804,17913,18054,18366,18442,18539,18618,18714,18802,18891,18976,19077,19152,19299,19579,19676,20400,20465,21142,22708,22782,22897,22992,23047,23140,23225,23292,23380,23468,23525,23603,24143,24220,24326,24398,24473,24540,24606,25088,25495,25587,25634,25682,25995,26053,26113,26293,26455,26579,26836,26920,27764,27861,28017,28099,28427,28516,28658,29289,29800,29885,35954,36086,36134,36289,36853,38729,38798,38862,38932,39020,39087,39138,40454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,120", "endOffsets": "161,282"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "2943,3054", "endColumns": "110,120", "endOffsets": "3049,3170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "71,78,146,150,451,455,456", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7069,7674,13013,13297,41811,42244,42323", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "7134,7756,13086,13434,41975,42318,42394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4855,4961,5120,5246,5355,5511,5641,5761,5994,6148,6255,6416,6544,6686,6862,6929,6991", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4956,5115,5241,5350,5506,5636,5756,5859,6143,6250,6411,6539,6681,6857,6924,6986,7064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,13601", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,13676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4780,7446,7761", "endColumns": "74,75,72", "endOffsets": "4850,7517,7829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,205,325,384,448,535,589,647,723,829,992,1261,1550,1624,1693,1777,1868,1989,2118,2191,2265,2428,2497,2556,2630,2711,2852,2981,3095,3181,3256,3341,3409", "endColumns": "70,78,119,58,63,86,53,57,75,105,162,268,288,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,113,85,74,84,67,155", "endOffsets": "121,200,320,379,443,530,584,642,718,824,987,1256,1545,1619,1688,1772,1863,1984,2113,2186,2260,2423,2492,2551,2625,2706,2847,2976,3090,3176,3251,3336,3404,3560"}, "to": {"startLines": "163,165,309,327,332,394,407,408,409,410,411,412,413,428,429,430,431,432,433,434,435,438,439,440,441,442,443,444,445,446,447,448,449,450", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14321,14481,27571,29294,29657,35819,37583,37637,37695,37771,37877,38040,38309,39664,39738,39807,39891,39982,40103,40232,40305,40511,40674,40743,40802,40876,40957,41098,41227,41341,41427,41502,41587,41655", "endColumns": "70,78,119,58,63,86,53,57,75,105,162,268,288,73,68,83,90,120,128,72,73,162,68,58,73,80,140,128,113,85,74,84,67,155", "endOffsets": "14387,14555,27686,29348,29716,35901,37632,37690,37766,37872,38035,38304,38593,39733,39802,39886,39977,40098,40227,40300,40374,40669,40738,40797,40871,40952,41093,41222,41336,41422,41497,41582,41650,41806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "170,173,176,178,179,180,187,188,190,191,192,193,194,195,196,198,199,202,213,214,226,228,229,263,264,276,287,288,290,297,298,307,308,316,317", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14847,15089,15352,15499,15583,15650,16138,16207,16344,16408,16476,16548,16608,16667,16740,16863,16933,17169,18059,18123,19157,19304,19389,23674,23772,24906,25687,25735,25865,26584,26651,27391,27485,28187,28275", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "14907,15162,15414,15578,15645,15706,16202,16269,16403,16471,16543,16603,16662,16735,16799,16928,16991,17239,18118,18207,19226,19384,19475,23767,23854,24949,25730,25807,25924,26646,26716,27480,27566,28270,28354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,452,453,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4606,4699,7249,7346,7522,8365,8441,12842,12931,13091,13155,13439,13519,13681,41980,42057,42124", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4694,4775,7341,7441,7605,8436,8524,12926,13008,13150,13214,13514,13596,13746,42052,42119,42239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,237,323,401,478,599,692,791,880,1001,1178,1366,1506,1597,1700,1793,1894,2061,2169,2268,2498,2604,2793,2887,2946,3010,3097,3194,3294,3405,3629,3698,3765,3839,3916,4005,4071,4152,4223,4299,4412,4498,4606,4696,4767,4855,4922,5008,5067,5169,5279,5384,5512,5573,5664,5760,5831,5932,6002,6089,6161,6267,6381,6468", "endColumns": "88,92,85,77,76,120,92,98,88,120,176,187,139,90,102,92,100,166,107,98,229,105,188,93,58,63,86,96,99,110,223,68,66,73,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,70,100,69,86,71,105,113,86,51", "endOffsets": "139,232,318,396,473,594,687,786,875,996,1173,1361,1501,1592,1695,1788,1889,2056,2164,2263,2493,2599,2788,2882,2941,3005,3092,3189,3289,3400,3624,3693,3760,3834,3911,4000,4066,4147,4218,4294,4407,4493,4601,4691,4762,4850,4917,5003,5062,5164,5274,5379,5507,5568,5659,5755,5826,5927,5997,6084,6156,6262,6376,6463,6515"}, "to": {"startLines": "164,235,247,248,249,266,277,279,280,329,336,337,338,341,343,345,346,347,348,349,350,351,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,414,423,424,425,426,427,437", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14392,20043,21820,21906,21984,23914,24954,25093,25192,29414,29950,30127,30315,30606,30797,30993,31086,31187,31354,31462,31561,31791,31978,32167,32261,32320,32384,32471,32568,32668,32779,33003,33072,33139,33213,33290,33379,33445,33526,33597,33673,34223,34309,34417,34507,34578,34666,34733,34819,34878,34980,35090,35195,35323,35384,35475,35571,36139,38598,39198,39285,39357,39463,39577,40459", "endColumns": "88,92,85,77,76,120,92,98,88,120,176,187,139,90,102,92,100,166,107,98,229,105,188,93,58,63,86,96,99,110,223,68,66,73,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,70,100,69,86,71,105,113,86,51", "endOffsets": "14476,20131,21901,21979,22056,24030,25042,25187,25276,29530,30122,30310,30450,30692,30895,31081,31182,31349,31457,31556,31786,31892,32162,32256,32315,32379,32466,32563,32663,32774,32998,33067,33134,33208,33285,33374,33440,33521,33592,33668,33781,34304,34412,34502,34573,34661,34728,34814,34873,34975,35085,35190,35318,35379,35470,35566,35637,36235,38663,39280,39352,39458,39572,39659,40506"}}]}]}