<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_sign_in_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\google_sign_in_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\geocoding_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\fluttertoast\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_maps_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\google_maps_flutter_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_custom_tabs_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\flutter_custom_tabs_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\connectivity_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\url_launcher_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\flutter_local_notifications\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\file_picker\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\geolocator_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="com.stripe:stripe-3ds2-android:6.1.8" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets"><file name="ds-amex.pem" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-amex.pem"/><file name="ds-cartesbancaires.pem" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-cartesbancaires.pem"/><file name="ds-discover.cer" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-discover.cer"/><file name="ds-mastercard.crt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-mastercard.crt"/><file name="ds-test-ec.txt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-test-ec.txt"/><file name="ds-test-rsa.txt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-test-rsa.txt"/><file name="ds-visa.crt" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\590721c84d64afcb0498097bcbbfea03\transformed\jetified-stripe-3ds2-android-6.1.8\assets\ds-visa.crt"/></source></dataSet><dataSet config="com.stripe:payments-core:20.52.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa82a6bcb6286a780b7683af7fc69878\transformed\jetified-payments-core-20.52.3\assets"><file name="au_becs_bsb.json" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa82a6bcb6286a780b7683af7fc69878\transformed\jetified-payments-core-20.52.3\assets\au_becs_bsb.json"/></source></dataSet><dataSet config="com.midtrans:uikit:1.31.1-SANDBOX" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1445e7ae71fec047506ab9dd37581292\transformed\jetified-uikit-1.31.1-SANDBOX\assets"><file name="bank_bins.json" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1445e7ae71fec047506ab9dd37581292\transformed\jetified-uikit-1.31.1-SANDBOX\assets\bank_bins.json"/><file name="bank_details.json" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1445e7ae71fec047506ab9dd37581292\transformed\jetified-uikit-1.31.1-SANDBOX\assets\bank_details.json"/><file name="country_code.json" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1445e7ae71fec047506ab9dd37581292\transformed\jetified-uikit-1.31.1-SANDBOX\assets\country_code.json"/><file name="credit_card_font.ttf" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1445e7ae71fec047506ab9dd37581292\transformed\jetified-uikit-1.31.1-SANDBOX\assets\credit_card_font.ttf"/></source></dataSet><dataSet config=":midpay" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\midpay\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_inappwebview_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\flutter_inappwebview_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_storage\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_messaging\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_database" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_database\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_crashlytics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_crashlytics\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\firebase_auth\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":cloud_firestore" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\cloud_firestore\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\webview_flutter_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":stripe_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\stripe_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":speech_to_text" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\speech_to_text\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\share_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":phonepe_payment_sdk" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\phonepe_payment_sdk\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":nb_utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\nb_utils\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_paystack" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\flutter_paystack\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\servicesProject\user_app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>