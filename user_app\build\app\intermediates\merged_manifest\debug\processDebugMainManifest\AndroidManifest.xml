<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.nafiss.user"
    android:versionCode="96"
    android:versionName="11.11.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="35" />
    <!--
         Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <queries>
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>

        <package android:name="com.gojek.app" />
        <package android:name="com.phonepe.app" />
        <package android:name="com.phonepe.app.preprod" />
        <package android:name="com.phonepe.simulator" />
        <package android:name="com.phonepe.simulator.debug" />

        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:host="pay"
                android:scheme="upi" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data
                android:host="upi"
                android:scheme="mandate" />
        </intent> <!-- Added to check the default browser that will host the AuthFlow. -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="http" />
        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
        <package android:name="com.android.chrome" />

        <intent>
            <action android:name="android.intent.action.GET_CONTENT" />

            <data android:mimeType="*/*" />
        </intent> <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <meta-data
        android:name="android.webkit.WebView.EnableSafeBrowsing"
        android:value="true" />

    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />

    <permission
        android:name="com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="TENDEEL User"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:usesCleartextTraffic="false" >
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_ic_notification" />

        <activity
            android:name="com.nafiss.user.MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize" >

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <!--
                 Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />

            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="com.google.firebase.messaging.default_notification_icon"
                android:resource="@drawable/ic_stat_ic_notification" />
            <meta-data
                android:name="com.google.firebase.messaging.default_notification_channel_id"
                android:value="notification" />
        </activity>
        <!--
        <activity
            android:name="com.braintreepayments.api.BraintreeBrowserSwitchActivity"
            android:exported="false"
            android:launchMode="singleTask"
            tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="${applicationId}.braintree" />
            </intent-filter>
        </activity>
        -->

        <meta-data
            android:name="com.google.android.gms.wallet.api.enabled"
            android:value="true" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="Your_Google_Map_key" />
        <!--
             Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <service
            android:name="com.nafiss.user.MyNavigationService"
            android:foregroundServiceType="location" />

        <activity
            android:name="co.paystack.flutterpaystack.AuthActivity"
            android:theme="@style/Paystack.Dialog" />
        <!--
           Declares a provider which allows us to store files to share in
           '.../caches/share_plus' and grant the receiving action access
        -->
        <provider
            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
            android:authorities="com.nafiss.user.flutter.share_provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_share_file_paths" />
        </provider>
        <!--
           This manifest declared broadcast receiver allows us to use an explicit
           Intent when creating a PendingItent to be informed of the user's choice
        -->
        <receiver
            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
            android:exported="false" >
            <intent-filter>
                <action android:name="EXTRA_CHOSEN_COMPONENT" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>
        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver>

        <provider
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
            android:authorities="com.nafiss.user.flutterfirebasemessaginginitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="genericidp" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="firebase.auth"
                    android:path="/"
                    android:scheme="recaptcha" />
            </intent-filter>
        </activity>

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config" />

        <service
            android:name="com.google.firebase.sessions.SessionLifecycleService"
            android:enabled="true"
            android:exported="false" />

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.nafiss.user.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
            android:exported="false"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
            android:exported="false"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
            android:exported="false"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@style/ThemeTransparent" />
        <activity
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@style/ThemeTransparent" />

        <receiver
            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
            android:enabled="true"
            android:exported="false" />

        <meta-data
            android:name="io.flutter.embedded_views_preview"
            android:value="true" />

        <activity
            android:name="com.midtrans.sdk.uikit.activities.UserDetailsActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.activities.PaymentMethodsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.mandiri_clickpay.MandiriClickPayActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.bri_epay.BriEpayPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.cimb_click.CimbClickPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.mandiri_ecash.MandiriEcashPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.indosat_dompetku.IndosatDompetkuPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.indomaret.payment.IndomaretPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.indomaret.status.IndomaretStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.telkomsel_cash.TelkomselCashPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.xl_tunai.payment.XlTunaiPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.xl_tunai.status.XlTunaiStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.xl_tunai.XlTunaiInstructionActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.kioson.payment.KiosonPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.kioson.status.KiosonStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.gci.GciPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.creditcard.details.CreditCardDetailsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.status.PaymentStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.creditcard.saved.SavedCreditCardActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.creditcard.bankpoints.BankPointsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.banktransfer.list.BankTransferListActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.banktransfer.payment.BankTransferPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.VaPaymentStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.VaOtherBankPaymentStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.MandiriBillStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.creditcard.register.CardRegistrationActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.gopay.payment.GoPayPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.shopeepay.payment.ShopeePayPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.gopay.status.GoPayStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.shopeepay.status.ShopeePayStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.danamon_online.DanamonOnlineActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.bca_klikbca.payment.KlikBcaPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.bca_klikbca.status.KlikBcaStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.bca_klikpay.BcaKlikPayPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.alfamart.payment.AlfamartPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.alfamart.status.AlfamartStatusActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.webview.WebViewPaymentActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.creditcard.tnc.TermsAndConditionsActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.midtrans.sdk.uikit.views.akulaku.AkulakuActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait" />
        <activity android:name="com.midtrans.sdk.uikit.views.uob.UobListActivity" />
        <activity android:name="com.midtrans.sdk.uikit.views.uob.app.UobAppPaymentActivity" />
        <activity android:name="com.midtrans.sdk.uikit.views.uob.web.UobWebPaymentActivity" />

        <service
            android:name="com.midtrans.raygun.RaygunPostService"
            android:exported="false"
            android:process=":raygunpostservice" />

        <activity
            android:name="com.phonepe.intent.sdk.ui.B2BPGActivity"
            android:exported="false"
            android:theme="@style/phonepeThemeInvisibleCompat" />
        <activity
            android:name="com.phonepe.intent.sdk.ui.TransactionActivity"
            android:configChanges="orientation"
            android:theme="@style/phonepeThemeInvisibleCompat"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <service
            android:name="com.phonepe.intent.sdk.ui.PreCacheService"
            android:exported="false" />

        <activity
            android:name="com.phonepe.intent.sdk.ui.OpenIntentTransactionActivity"
            android:theme="@style/phonepeThemeInvisibleCompat" />
        <activity
            android:name="com.phonepe.intent.sdk.ui.UpiAppsSelectionDialogActivity"
            android:theme="@style/phonepeThemeInvisible" />
        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
                <data
                    android:host="link-accounts"
                    android:pathPrefix="/com.nafiss.user/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
                <data
                    android:host="link-native-accounts"
                    android:pathPrefix="/com.nafiss.user/authentication_return"
                    android:scheme="stripe-auth" />

                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
                <data
                    android:host="link-accounts"
                    android:path="/com.nafiss.user/success"
                    android:scheme="stripe-auth" />
                <data
                    android:host="link-accounts"
                    android:path="/com.nafiss.user/cancel"
                    android:scheme="stripe-auth" />

                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
                <data
                    android:host="native-redirect"
                    android:pathPrefix="/com.nafiss.user"
                    android:scheme="stripe-auth" />

                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
                <data
                    android:host="auth-redirect"
                    android:pathPrefix="/com.nafiss.user"
                    android:scheme="stripe" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
        <activity
            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
            android:exported="false"
            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.link.LinkActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTop"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.link.LinkForegroundActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:launchMode="singleTop"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
            android:autoRemoveFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="complete"
                    android:path="/com.nafiss.user"
                    android:scheme="link-popup" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
            android:exported="false"
            android:theme="@style/StripePaymentSheetDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.AddPaymentMethodActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentMethodsActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentFlowActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.view.PaymentRelayActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" />
        <!--
        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
        launched the browser Activity will also handle the return URL deep link.
        -->
        <activity
            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/StripeTransparentTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Must match `DefaultReturnUrl#value`. -->
                <data
                    android:host="payment_return_url"
                    android:path="/com.nafiss.user"
                    android:scheme="stripesdk" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
            android:exported="false"
            android:theme="@style/StripeDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
            android:exported="false"
            android:theme="@style/StripeGooglePayDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
            android:exported="false"
            android:theme="@style/StripePayLauncherDefaultTheme" />
        <activity
            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
            android:exported="false"
            android:theme="@style/StripeTransparentTheme" />
        <activity
            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
            android:exported="false"
            android:theme="@style/Stripe3DS2Theme" />

        <service
            android:name="com.baseflow.geolocator.GeolocatorLocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <provider
            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
            android:authorities="com.nafiss.user.flutter.image_provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/flutter_image_picker_file_paths" />
        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>

            <meta-data
                android:name="photopicker_activity:0:required"
                android:value="" />
        </service>

        <activity
            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
            android:exported="false"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:clearTaskOnLaunch="true"
            android:screenOrientation="sensorLandscape"
            android:stateNotNeeded="true"
            android:theme="@style/zxing_CaptureTheme"
            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
            android:enabled="true"
            android:exported="false" >
            <meta-data
                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
        </service>

        <activity
            android:name="androidx.credentials.playservices.HiddenActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:enabled="true"
            android:exported="false"
            android:fitsSystemWindows="true"
            android:theme="@style/Theme.Hidden" >
        </activity>
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.nafiss.user.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />
        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
        <activity
            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
            android:exported="false"
            android:stateNotNeeded="true"
            android:theme="@style/Theme.PlayCore.Transparent" />

        <meta-data
            android:name="aia-compat-api-min-version"
            android:value="1" />
    </application>

</manifest>