{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,217,354,429,494,590,652,710,789,913,1093,1411,1747,1840,1910,2002,2099,2230,2372,2451,2523,2686,2755,2816,2892,2986,3133,3251,3360,3452,3528,3610,3687", "endColumns": "78,82,136,74,64,95,61,57,78,123,179,317,335,92,69,91,96,130,141,78,71,162,68,60,75,93,146,117,108,91,75,81,76,157", "endOffsets": "129,212,349,424,489,585,647,705,784,908,1088,1406,1742,1835,1905,1997,2094,2225,2367,2446,2518,2681,2750,2811,2887,2981,3128,3246,3355,3447,3523,3605,3682,3840"}, "to": {"startLines": "170,172,316,334,339,401,414,415,416,417,418,419,420,435,436,437,438,439,440,441,442,445,446,447,448,449,450,451,452,453,454,455,456,457", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15704,15870,29891,31836,32236,38856,40925,40987,41045,41124,41248,41428,41746,43279,43372,43442,43534,43631,43762,43904,43983,44189,44352,44421,44482,44558,44652,44799,44917,45026,45118,45194,45276,45353", "endColumns": "78,82,136,74,64,95,61,57,78,123,179,317,335,92,69,91,96,130,141,78,71,162,68,60,75,93,146,117,108,91,75,81,76,157", "endOffsets": "15778,15948,30023,31906,32296,38947,40982,41040,41119,41243,41423,41741,42077,43367,43437,43529,43626,43757,43899,43978,44050,44347,44416,44477,44553,44647,44794,44912,45021,45113,45189,45271,45348,45506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "6168", "endColumns": "160", "endOffsets": "6324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14659,14749,14855,14922,14990,15055,15130", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "14744,14850,14917,14985,15050,15125,15193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,325,426,637,684,756,860,935,1208,1271,1366,1440,1495,1559,1883,1950,2017,2072,2127,2221,2371,2484,2543,2630,2718,2811,2882,2985,3287,3368,3447,3524,3586,3648,3726,3801,3880,3989,4094,4193,4311,4414,4488,4567,4658,4856,5066,5199,5346,5408,6275,6340", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,64,57", "endOffsets": "157,320,421,632,679,751,855,930,1203,1266,1361,1435,1490,1554,1878,1945,2012,2067,2122,2216,2366,2479,2538,2625,2713,2806,2877,2980,3282,3363,3442,3519,3581,3643,3721,3796,3875,3984,4089,4188,4306,4409,4483,4562,4653,4851,5061,5194,5341,5403,6270,6335,6393"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,309,310,311,312,313,319,322,327,329,330,331,332,335,337,338,342,346,347,349,351,359,379,380,381,382,383,400,407,408,409,410,412,413,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21276,21383,21546,21734,22856,22903,22975,23079,23154,23427,23490,25597,25871,26078,26678,27407,27474,27975,28976,29188,29282,29432,29545,29604,30222,30510,31006,31157,31260,31562,31643,31911,32112,32174,32484,33105,33180,33349,33568,34683,36645,36763,36866,36940,37019,38658,39372,39582,39715,39862,39993,40860,42673", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,64,57", "endOffsets": "21378,21541,21642,21940,22898,22970,23074,23149,23422,23485,23580,25666,25921,26137,26997,27469,27536,28025,29026,29277,29427,29540,29599,29686,30305,30598,31072,31255,31557,31638,31717,31983,32169,32231,32557,33175,33254,33453,33668,34777,36758,36861,36935,37014,37105,38851,39577,39710,39857,39919,40855,40920,42726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "71,78,146,150,458,462,463", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7520,8128,13781,14086,45511,45950,46036", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "7585,8220,13853,14224,45675,46031,46111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "5030,7901,8225", "endColumns": "74,73,76", "endOffsets": "5100,7970,8297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5105,5211,5391,5521,5630,5801,5934,6055,6329,6524,6636,6821,6957,7117,7296,7369,7436", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "5206,5386,5516,5625,5796,5929,6050,6163,6519,6631,6816,6952,7112,7291,7364,7431,7515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16329,17152,17294,17356,17438,17729,18260,18452,18703,19033,19381,19672", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "16406,17207,17351,17433,17495,17795,18313,18529,18771,19095,19436,19737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,459,460,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4843,4942,7697,7795,7975,8866,8946,13602,13694,13858,13929,14229,14310,14482,45680,45759,45828", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4937,5025,7790,7896,8057,8941,9035,13689,13776,13924,13992,14305,14390,14553,45754,45823,45945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,14395", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,14477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3083,3199", "endColumns": "115,118", "endOffsets": "3194,3313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,3010,3103,3182,3237,3288,3354,3433,3514,3605,3685,3757,3835,3910,3982,4093,4190,4267,4365,4463,4541,4622,4722,4779,4863,4929,5012,5099,5161,5225,5288,5364,5466,5573,5670,5776,5835,5890", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,3005,3098,3177,3232,3283,3349,3428,3509,3600,3680,3752,3830,3905,3977,4088,4185,4262,4360,4458,4536,4617,4717,4774,4858,4924,5007,5094,5156,5220,5283,5359,5461,5568,5665,5771,5830,5885,5974"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3318,3425,3533,3615,3716,4536,4636,4758,8062,8302,8786,9040,9102,9194,9261,9335,9396,9475,9539,9593,9709,9768,9830,9884,9966,10095,10187,10271,10415,10494,10575,10722,10815,10894,10949,11000,11066,11145,11226,11317,11397,11469,11547,11622,11694,11805,11902,11979,12077,12175,12253,12334,12434,12491,12575,12641,12724,12811,12873,12937,13000,13076,13178,13285,13382,13488,13547,13997", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,146,92,78,54,50,65,78,80,90,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88", "endOffsets": "328,3420,3528,3610,3711,3808,4631,4753,4838,8123,8394,8861,9097,9189,9256,9330,9391,9470,9534,9588,9704,9763,9825,9879,9961,10090,10182,10266,10410,10489,10570,10717,10810,10889,10944,10995,11061,11140,11221,11312,11392,11464,11542,11617,11689,11800,11897,11974,12072,12170,12248,12329,12429,12486,12570,12636,12719,12806,12868,12932,12995,13071,13173,13280,13377,13483,13542,13597,14081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7590,8399,8501,8620", "endColumns": "106,101,118,104", "endOffsets": "7692,8496,8615,8720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,314,315,323,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16264,16515,16788,16935,17020,17091,17584,17656,17800,17864,17932,18002,18062,18122,18196,18318,18389,18638,19523,19588,20680,20849,20941,25671,25789,27002,27828,27878,28030,28830,28905,29691,29802,30603,30715", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "16324,16592,16850,17015,17086,17147,17651,17724,17859,17927,17997,18057,18117,18191,18255,18384,18447,18698,19583,19667,20756,20936,21034,25784,25866,27048,27873,27970,28087,28900,28971,29797,29886,30710,30822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "84,464", "startColumns": "4,4", "startOffsets": "8725,46116", "endColumns": "60,78", "endOffsets": "8781,46190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3813,3911,4013,4112,4214,4318,4422,14558", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3906,4008,4107,4209,4313,4417,4531,14654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3400,3483,3548,4311,5050,5129,5243,5354,5409,5520,5632,5706,5812,5918,5974,6062,6112,6195,6313,6388,6465,6532,6598,6646,6734,6836,6884,6933,7002,7060,7125,7326,7522,7671,7739,7828,7914,8022,8121,8222,8305,8401,8481,8595,8677,8778,8832,8977,9029,9084,9153,9223,9298,9368,9442,9532,9608,9664", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3395,3478,3543,4306,5045,5124,5238,5349,5404,5515,5627,5701,5807,5913,5969,6057,6107,6190,6308,6383,6460,6527,6593,6641,6729,6831,6879,6928,6997,7055,7120,7321,7517,7666,7734,7823,7909,8017,8116,8217,8300,8396,8476,8590,8672,8773,8827,8972,9024,9079,9148,9218,9293,9363,9437,9527,9603,9659,9738"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,317,318,320,321,325,326,328,333,340,341,402,403,404,406,411,422,423,424,425,426,427,428,443", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15198,15271,15353,15415,15491,15576,15637,15953,16004,16089,16177,16411,16597,16701,16855,17212,17500,18534,18776,18871,18941,19100,19189,19274,19441,19742,19834,19911,20010,20093,20200,20300,20396,20488,20598,20761,21039,21161,21945,22028,22093,23846,24585,24664,24778,24889,24944,25055,25167,25241,25347,25453,25509,26142,26192,26275,26393,26468,26545,26612,27140,27541,27629,27731,27779,28092,28161,28219,28284,28485,28681,29031,29099,30028,30114,30310,30409,30827,30910,31077,31722,32301,32383,38952,39006,39151,39317,39924,42162,42232,42307,42377,42451,42541,42617,44055", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "15266,15348,15410,15486,15571,15632,15699,15999,16084,16172,16259,16510,16696,16783,16930,17289,17579,18633,18866,18936,19028,19184,19269,19376,19518,19829,19906,20005,20088,20195,20295,20391,20483,20593,20675,20844,21156,21271,22023,22088,22851,24580,24659,24773,24884,24939,25050,25162,25236,25342,25448,25504,25592,26187,26270,26388,26463,26540,26607,26673,27183,27624,27726,27774,27823,28156,28214,28279,28480,28676,28825,29094,29183,30109,30217,30404,30505,30905,31001,31152,31831,32378,32479,39001,39146,39198,39367,39988,42227,42302,42372,42446,42536,42612,42668,44129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,229,323,407,490,642,729,845,948,1072,1243,1442,1615,1705,1815,1917,2019,2214,2341,2453,2714,2825,3027,3126,3187,3253,3338,3440,3540,3645,3889,3964,4036,4117,4192,4276,4343,4427,4498,4567,4688,4785,4898,5001,5078,5174,5245,5340,5405,5520,5637,5748,5888,5953,6052,6164,6236,6350,6430,6531,6607,6732,6889,6978", "endColumns": "86,86,93,83,82,151,86,115,102,123,170,198,172,89,109,101,101,194,126,111,260,110,201,98,60,65,84,101,99,104,243,74,71,80,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,71,113,79,100,75,124,156,88,54", "endOffsets": "137,224,318,402,485,637,724,840,943,1067,1238,1437,1610,1700,1810,1912,2014,2209,2336,2448,2709,2820,3022,3121,3182,3248,3333,3435,3535,3640,3884,3959,4031,4112,4187,4271,4338,4422,4493,4562,4683,4780,4893,4996,5073,5169,5240,5335,5400,5515,5632,5743,5883,5948,6047,6159,6231,6345,6425,6526,6602,6727,6884,6973,7028"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,336,343,344,345,348,350,352,353,354,355,356,357,358,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,405,421,430,431,432,433,434,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15783,21647,23585,23679,23763,25926,27053,27188,27304,31988,32562,32733,32932,33259,33458,33673,33775,33877,34072,34199,34311,34572,34782,34984,35083,35144,35210,35295,35397,35497,35602,35846,35921,35993,36074,36149,36233,36300,36384,36455,36524,37110,37207,37320,37423,37500,37596,37667,37762,37827,37942,38059,38170,38310,38375,38474,38586,39203,42082,42731,42832,42908,43033,43190,44134", "endColumns": "86,86,93,83,82,151,86,115,102,123,170,198,172,89,109,101,101,194,126,111,260,110,201,98,60,65,84,101,99,104,243,74,71,80,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,71,113,79,100,75,124,156,88,54", "endOffsets": "15865,21729,23674,23758,23841,26073,27135,27299,27402,32107,32728,32927,33100,33344,33563,33770,33872,34067,34194,34306,34567,34678,34979,35078,35139,35205,35290,35392,35492,35597,35841,35916,35988,36069,36144,36228,36295,36379,36450,36519,36640,37202,37315,37418,37495,37591,37662,37757,37822,37937,38054,38165,38305,38370,38469,38581,38653,39312,42157,42827,42903,43028,43185,43274,44184"}}]}]}