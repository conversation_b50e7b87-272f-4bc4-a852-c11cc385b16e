{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,354,355,362,366,580,583", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25957,26073,26530,26824,39620,39792", "endLines": "2,17,18,19,354,355,362,366,582,587", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,26068,26194,26651,26947,39787,40139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,32,35,38,41,44,47,50,53,56,59,60,63,68,79,85,94,103,112,121,130,139,148,157,166,175,184,193,202,211,220,226,232,238,244,248,252,253,254,255,259,262,265,268,271,272,275,278,282,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1386,1476,1572,1662,1755,1862,1967,2086,2211,2332,2545,2804,3075,3293,3525,3761,4011,4224,4433,4664,4865,4981,5151,5472,6501,6958,7462,7970,8479,8993,9498,10002,10507,11013,11515,12021,12530,13038,13537,14044,14552,14844,15138,15438,15738,16067,16408,16546,16690,16846,17239,17457,17679,17905,18121,18231,18401,18591,18832,19091", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,31,34,37,40,43,46,49,52,55,58,59,62,67,78,84,93,102,111,120,129,138,147,156,165,174,183,192,201,210,219,225,231,237,243,247,251,252,253,254,258,261,264,267,270,271,274,277,281,285,288", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1381,1471,1567,1657,1750,1857,1962,2081,2206,2327,2540,2799,3070,3288,3520,3756,4006,4219,4428,4659,4860,4976,5146,5467,6496,6953,7457,7965,8474,8988,9493,9997,10502,11008,11510,12016,12525,13033,13532,14039,14547,14839,15133,15433,15733,16062,16403,16541,16685,16841,17234,17452,17674,17900,18116,18226,18396,18586,18827,19086,19263"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,21,22,23,24,85,86,87,88,90,91,92,95,98,193,196,199,202,208,211,214,281,284,285,288,293,304,370,379,388,397,406,415,424,433,442,451,460,469,478,487,496,505,511,517,523,537,541,545,546,547,548,552,555,558,561,588,589,592,595,599,603", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,1668,1764,1854,1950,7407,7500,7607,7712,7934,8059,8180,8393,8652,14833,15051,15283,15519,15968,16181,16390,21177,21378,21494,21664,21985,23014,27119,27623,28131,28640,29154,29659,30163,30668,31174,31676,32182,32691,33199,33698,34205,34713,35005,35299,35599,36392,36721,37062,37200,37344,37500,37893,38111,38333,38559,40144,40254,40424,40614,40855,41114", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,21,22,23,24,85,86,87,88,90,91,94,97,100,195,198,201,204,210,213,216,283,284,287,292,303,309,378,387,396,405,414,423,432,441,450,459,468,477,486,495,504,510,516,522,528,540,544,545,546,547,551,554,557,560,563,588,591,594,598,602,605", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,95,89,95,89,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,1759,1849,1945,2035,7495,7602,7707,7826,8054,8175,8388,8647,8918,15046,15278,15514,15764,16176,16385,16616,21373,21489,21659,21980,23009,23466,27618,28126,28635,29149,29654,30158,30663,31169,31671,32177,32686,33194,33693,34200,34708,35000,35294,35594,35894,36716,37057,37195,37339,37495,37888,38106,38328,38554,38770,40249,40419,40609,40850,41109,41286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\26b716dd4ce231a28c886a6457744867\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "9", "endColumns": "12", "endOffsets": "543"}, "to": {"startLines": "529", "startColumns": "4", "startOffsets": "35899", "endLines": "536", "endColumns": "12", "endOffsets": "36387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22bbc9bab209556a742f74d50540c114\\transformed\\jetified-uikit-1.31.1-SANDBOX\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,7,12", "startColumns": "4,4,4", "startOffsets": "55,344,607", "endLines": "6,11,17", "endColumns": "12,12,12", "endOffsets": "339,602,895"}, "to": {"startLines": "564,569,574", "startColumns": "4,4,4", "startOffsets": "38775,39064,39327", "endLines": "568,573,579", "endColumns": "12,12,12", "endOffsets": "39059,39322,39615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a4dccb56f227b56afcf95779216ccc46\\transformed\\media-1.1.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "356,359,363,367", "startColumns": "4,4,4,4", "startOffsets": "26199,26367,26656,26952", "endLines": "358,361,365,369", "endColumns": "12,12,12,12", "endOffsets": "26362,26525,26819,27114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "20,348,351", "startColumns": "4,4,4", "startOffsets": "1603,25623,25779", "endLines": "20,350,353", "endColumns": "64,12,12", "endOffsets": "1663,25774,25952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,42,43,44,45,47,49,50,51,52,53,55,57,59,61,63,65,66,71,73,75,76,77,79,81,82,83,84,89,101,144,147,190,205,217,219,221,223,226,230,233,234,235,238,239,240,241,242,243,246,247,249,251,253,255,259,261,262,263,264,266,270,272,274,275,276,277,278,279,310,311,312,322,323,324,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2040,2131,2234,2337,2442,2549,2658,2767,2876,2985,3094,3201,3304,3423,3578,3733,3838,3959,4060,4207,4348,4451,4570,4677,4780,4935,5106,5255,5420,5577,5728,5847,6198,6347,6496,6608,6755,6908,7055,7130,7219,7306,7831,8923,11681,11866,14636,15769,16621,16744,16867,16980,17163,17418,17619,17708,17819,18052,18153,18248,18371,18500,18617,18794,18893,19028,19171,19306,19425,19626,19745,19838,19949,20005,20112,20307,20418,20551,20646,20737,20828,20921,21038,23471,23542,23625,24248,24305,24363,24987", "endLines": "25,26,27,28,29,30,31,32,33,34,35,36,37,39,41,42,43,44,46,48,49,50,51,52,54,56,58,60,62,64,65,70,72,74,75,76,78,80,81,82,83,84,89,143,146,189,192,207,218,220,222,225,229,232,233,234,237,238,239,240,241,242,245,246,248,250,252,254,258,260,261,262,263,265,269,271,273,274,275,276,277,278,280,310,311,321,322,323,335,347", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "2126,2229,2332,2437,2544,2653,2762,2871,2980,3089,3196,3299,3418,3573,3728,3833,3954,4055,4202,4343,4446,4565,4672,4775,4930,5101,5250,5415,5572,5723,5842,6193,6342,6491,6603,6750,6903,7050,7125,7214,7301,7402,7929,11676,11861,14631,14828,15963,16739,16862,16975,17158,17413,17614,17703,17814,18047,18148,18243,18366,18495,18612,18789,18888,19023,19166,19301,19420,19621,19740,19833,19944,20000,20107,20302,20413,20546,20641,20732,20823,20916,21033,21172,23537,23620,24243,24300,24358,24982,25618"}}]}]}