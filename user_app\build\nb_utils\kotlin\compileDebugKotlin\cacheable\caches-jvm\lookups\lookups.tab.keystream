  R android  color 	android.R  system_accent1_0 android.R.color  system_accent1_10 android.R.color  system_accent1_100 android.R.color  system_accent1_1000 android.R.color  system_accent1_200 android.R.color  system_accent1_300 android.R.color  system_accent1_400 android.R.color  system_accent1_50 android.R.color  system_accent1_500 android.R.color  system_accent1_600 android.R.color  system_accent1_700 android.R.color  system_accent1_800 android.R.color  system_accent1_900 android.R.color  system_accent2_0 android.R.color  system_accent2_10 android.R.color  system_accent2_100 android.R.color  system_accent2_1000 android.R.color  system_accent2_200 android.R.color  system_accent2_300 android.R.color  system_accent2_400 android.R.color  system_accent2_50 android.R.color  system_accent2_500 android.R.color  system_accent2_600 android.R.color  system_accent2_700 android.R.color  system_accent2_800 android.R.color  system_accent2_900 android.R.color  system_accent3_0 android.R.color  system_accent3_10 android.R.color  system_accent3_100 android.R.color  system_accent3_1000 android.R.color  system_accent3_200 android.R.color  system_accent3_300 android.R.color  system_accent3_400 android.R.color  system_accent3_50 android.R.color  system_accent3_500 android.R.color  system_accent3_600 android.R.color  system_accent3_700 android.R.color  system_accent3_800 android.R.color  system_accent3_900 android.R.color  system_neutral1_0 android.R.color  system_neutral1_10 android.R.color  system_neutral1_100 android.R.color  system_neutral1_1000 android.R.color  system_neutral1_200 android.R.color  system_neutral1_300 android.R.color  system_neutral1_400 android.R.color  system_neutral1_50 android.R.color  system_neutral1_500 android.R.color  system_neutral1_600 android.R.color  system_neutral1_700 android.R.color  system_neutral1_800 android.R.color  system_neutral1_900 android.R.color  system_neutral2_0 android.R.color  system_neutral2_10 android.R.color  system_neutral2_100 android.R.color  system_neutral2_1000 android.R.color  system_neutral2_200 android.R.color  system_neutral2_300 android.R.color  system_neutral2_400 android.R.color  system_neutral2_50 android.R.color  system_neutral2_500 android.R.color  system_neutral2_600 android.R.color  system_neutral2_700 android.R.color  system_neutral2_800 android.R.color  system_neutral2_900 android.R.color  Context android.content  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getRESOURCES android.content.Context  getResources android.content.Context  	getString android.content.Context  getTHEME android.content.Context  getTheme android.content.Context  packageManager android.content.Context  packageName android.content.Context  	resources android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  setResources android.content.Context  setTheme android.content.Context  theme android.content.Context  ApplicationInfo android.content.pm  PackageInfo android.content.pm  PackageManager android.content.pm  labelRes "android.content.pm.ApplicationInfo  nonLocalizedLabel "android.content.pm.ApplicationInfo  applicationInfo android.content.pm.PackageInfo  getLONGVersionCode android.content.pm.PackageInfo  getLongVersionCode android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  packageName android.content.pm.PackageInfo  setLongVersionCode android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  PackageInfoFlags !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  of 2android.content.pm.PackageManager.PackageInfoFlags  	Resources android.content.res  Theme android.content.res.Resources  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  NonNull androidx.annotation  ResourcesCompat androidx.core.content.res  getColor )androidx.core.content.res.ResourcesCompat  Any com.example.nb_utils  Build com.example.nb_utils  Integer com.example.nb_utils  Long com.example.nb_utils  Map com.example.nb_utils  
MethodChannel com.example.nb_utils  
NbUtilsPlugin com.example.nb_utils  PackageManager com.example.nb_utils  ResourcesCompat com.example.nb_utils  String com.example.nb_utils  Suppress com.example.nb_utils  android com.example.nb_utils  
component1 com.example.nb_utils  
component2 com.example.nb_utils  map com.example.nb_utils  mapOf com.example.nb_utils  to com.example.nb_utils  toMap com.example.nb_utils  Any "com.example.nb_utils.NbUtilsPlugin  Build "com.example.nb_utils.NbUtilsPlugin  Context "com.example.nb_utils.NbUtilsPlugin  
FlutterPlugin "com.example.nb_utils.NbUtilsPlugin  Integer "com.example.nb_utils.NbUtilsPlugin  Long "com.example.nb_utils.NbUtilsPlugin  Map "com.example.nb_utils.NbUtilsPlugin  
MethodCall "com.example.nb_utils.NbUtilsPlugin  
MethodChannel "com.example.nb_utils.NbUtilsPlugin  PackageInfo "com.example.nb_utils.NbUtilsPlugin  PackageManager "com.example.nb_utils.NbUtilsPlugin  ResourcesCompat "com.example.nb_utils.NbUtilsPlugin  Result "com.example.nb_utils.NbUtilsPlugin  String "com.example.nb_utils.NbUtilsPlugin  Suppress "com.example.nb_utils.NbUtilsPlugin  android "com.example.nb_utils.NbUtilsPlugin  
appContext "com.example.nb_utils.NbUtilsPlugin  channel "com.example.nb_utils.NbUtilsPlugin  
component1 "com.example.nb_utils.NbUtilsPlugin  
component2 "com.example.nb_utils.NbUtilsPlugin  
getANDROID "com.example.nb_utils.NbUtilsPlugin  
getAndroid "com.example.nb_utils.NbUtilsPlugin  
getComponent1 "com.example.nb_utils.NbUtilsPlugin  
getComponent2 "com.example.nb_utils.NbUtilsPlugin  getLongVersionCode "com.example.nb_utils.NbUtilsPlugin  getMAP "com.example.nb_utils.NbUtilsPlugin  getMAPOf "com.example.nb_utils.NbUtilsPlugin  getMap "com.example.nb_utils.NbUtilsPlugin  getMapOf "com.example.nb_utils.NbUtilsPlugin  getMaterialYouColours "com.example.nb_utils.NbUtilsPlugin  getTO "com.example.nb_utils.NbUtilsPlugin  getTOMap "com.example.nb_utils.NbUtilsPlugin  getTo "com.example.nb_utils.NbUtilsPlugin  getToMap "com.example.nb_utils.NbUtilsPlugin  map "com.example.nb_utils.NbUtilsPlugin  mapOf "com.example.nb_utils.NbUtilsPlugin  packageInfo "com.example.nb_utils.NbUtilsPlugin  to "com.example.nb_utils.NbUtilsPlugin  toMap "com.example.nb_utils.NbUtilsPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  Integer 	java.lang  
MethodChannel 	java.lang  PackageManager 	java.lang  ResourcesCompat 	java.lang  android 	java.lang  
component1 	java.lang  
component2 	java.lang  map 	java.lang  mapOf 	java.lang  to 	java.lang  toMap 	java.lang  toHexString java.lang.Integer  Any kotlin  Boolean kotlin  Build kotlin  	Function1 kotlin  Int kotlin  Integer kotlin  Long kotlin  
MethodChannel kotlin  Nothing kotlin  PackageManager kotlin  Pair kotlin  ResourcesCompat kotlin  String kotlin  Suppress kotlin  android kotlin  
component1 kotlin  
component2 kotlin  map kotlin  mapOf kotlin  to kotlin  toMap kotlin  getTO 
kotlin.String  getTo 
kotlin.String  Build kotlin.annotation  Integer kotlin.annotation  
MethodChannel kotlin.annotation  PackageManager kotlin.annotation  ResourcesCompat kotlin.annotation  android kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  to kotlin.annotation  toMap kotlin.annotation  Build kotlin.collections  Integer kotlin.collections  Map kotlin.collections  
MethodChannel kotlin.collections  PackageManager kotlin.collections  ResourcesCompat kotlin.collections  android kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  map kotlin.collections  mapOf kotlin.collections  to kotlin.collections  toMap kotlin.collections  getTOMap kotlin.collections.List  getToMap kotlin.collections.List  Entry kotlin.collections.Map  getMAP kotlin.collections.Map  getMap kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  Build kotlin.comparisons  Integer kotlin.comparisons  
MethodChannel kotlin.comparisons  PackageManager kotlin.comparisons  ResourcesCompat kotlin.comparisons  android kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  to kotlin.comparisons  toMap kotlin.comparisons  Build 	kotlin.io  Integer 	kotlin.io  
MethodChannel 	kotlin.io  PackageManager 	kotlin.io  ResourcesCompat 	kotlin.io  android 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  to 	kotlin.io  toMap 	kotlin.io  Build 
kotlin.jvm  Integer 
kotlin.jvm  
MethodChannel 
kotlin.jvm  PackageManager 
kotlin.jvm  ResourcesCompat 
kotlin.jvm  android 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  to 
kotlin.jvm  toMap 
kotlin.jvm  Build 
kotlin.ranges  Integer 
kotlin.ranges  
MethodChannel 
kotlin.ranges  PackageManager 
kotlin.ranges  ResourcesCompat 
kotlin.ranges  android 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  to 
kotlin.ranges  toMap 
kotlin.ranges  Build kotlin.sequences  Integer kotlin.sequences  
MethodChannel kotlin.sequences  PackageManager kotlin.sequences  ResourcesCompat kotlin.sequences  android kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  to kotlin.sequences  toMap kotlin.sequences  Build kotlin.text  Integer kotlin.text  
MethodChannel kotlin.text  PackageManager kotlin.text  ResourcesCompat kotlin.text  android kotlin.text  
component1 kotlin.text  
component2 kotlin.text  map kotlin.text  mapOf kotlin.text  to kotlin.text  toMap kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                