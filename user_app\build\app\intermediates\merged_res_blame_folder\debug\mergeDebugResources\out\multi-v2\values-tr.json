{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce885bcb1f688bf29e6307e707f1ebc4\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7236,8019,8125,8232", "endColumns": "99,105,106,105", "endOffsets": "7331,8120,8227,8333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3237,3312,3387,3464,3563,4367,4463,4575,7696,7928,8399,8659,8720,8811,8874,8937,8996,9065,9128,9182,9290,9348,9410,9464,9537,9658,9742,9833,9973,10050,10126,10257,10344,10420,10473,10527,10593,10663,10740,10823,10903,10974,11049,11127,11198,11299,11384,11473,11568,11661,11733,11805,11901,11953,12039,12106,12190,12280,12342,12406,12469,12539,12633,12735,12824,12924,12981,13423", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "310,3307,3382,3459,3558,3649,4458,4570,4652,7755,8014,8471,8715,8806,8869,8932,8991,9060,9123,9177,9285,9343,9405,9459,9532,9653,9737,9828,9968,10045,10121,10252,10339,10415,10468,10522,10588,10658,10735,10818,10898,10969,11044,11122,11193,11294,11379,11468,11563,11656,11728,11800,11896,11948,12034,12101,12185,12275,12337,12401,12464,12534,12628,12730,12819,12919,12976,13034,13497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,198,328,391,453,545,604,661,740,845,1016,1276,1561,1648,1721,1801,1892,2022,2173,2250,2324,2503,2569,2630,2704,2793,2934,3059,3174,3258,3340,3426,3495", "endColumns": "69,72,129,62,61,91,58,56,78,104,170,259,284,86,72,79,90,129,150,76,73,178,65,60,73,88,140,124,114,83,81,85,68,160", "endOffsets": "120,193,323,386,448,540,599,656,735,840,1011,1271,1556,1643,1716,1796,1887,2017,2168,2245,2319,2498,2564,2625,2699,2788,2929,3054,3169,3253,3335,3421,3490,3651"}, "to": {"startLines": "170,172,317,335,340,402,415,416,417,418,419,420,421,436,437,438,439,440,441,442,443,446,447,448,449,450,451,452,453,454,455,456,457,458", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15079,15231,28481,30279,30640,36945,38855,38914,38971,39050,39155,39326,39586,40961,41048,41121,41201,41292,41422,41573,41650,41863,42042,42108,42169,42243,42332,42473,42598,42713,42797,42879,42965,43034", "endColumns": "69,72,129,62,61,91,58,56,78,104,170,259,284,86,72,79,90,129,150,76,73,178,65,60,73,88,140,124,114,83,81,85,68,160", "endOffsets": "15144,15299,28606,30337,30697,37032,38909,38966,39045,39150,39321,39581,39866,41043,41116,41196,41287,41417,41568,41645,41719,42037,42103,42164,42238,42327,42468,42593,42708,42792,42874,42960,43029,43190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,345,431,491,557,609,689,765,846,933,1035,1122,1204,1288,1373,1476,1572,1641,1734,1821,1900,2006,2092,2185,2261,2361,2439,2536,2623,2706,2789,2887,2963,3046,3155,3257,3326,3393,4055,4693,4770,4876,4975,5030,5114,5198,5266,5355,5447,5502,5582,5630,5706,5812,5881,5951,6018,6084,6131,6214,6311,6357,6405,6487,6545,6612,6786,6940,7069,7137,7217,7291,7378,7458,7540,7611,7699,7767,7866,7947,8037,8092,8215,8264,8321,8386,8448,8518,8585,8654,8741,8809,8860", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "121,201,263,340,426,486,552,604,684,760,841,928,1030,1117,1199,1283,1368,1471,1567,1636,1729,1816,1895,2001,2087,2180,2256,2356,2434,2531,2618,2701,2784,2882,2958,3041,3150,3252,3321,3388,4050,4688,4765,4871,4970,5025,5109,5193,5261,5350,5442,5497,5577,5625,5701,5807,5876,5946,6013,6079,6126,6209,6306,6352,6400,6482,6540,6607,6781,6935,7064,7132,7212,7286,7373,7453,7535,7606,7694,7762,7861,7942,8032,8087,8210,8259,8316,8381,8443,8513,8580,8649,8736,8804,8855,8931"}, "to": {"startLines": "163,164,165,166,167,168,169,173,174,175,176,179,181,182,184,189,193,208,211,212,213,215,216,217,219,223,224,225,226,227,228,229,230,231,232,234,237,238,244,245,246,257,258,259,260,261,262,263,264,265,266,267,268,275,276,277,278,279,280,281,285,290,291,292,293,298,299,300,301,302,303,307,308,318,319,321,322,326,327,329,334,341,342,403,404,405,407,412,423,424,425,426,427,428,429,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14577,14648,14728,14790,14867,14953,15013,15304,15356,15436,15512,15734,15899,16001,16156,16518,16808,17837,18097,18193,18262,18418,18505,18584,18752,19051,19144,19220,19320,19398,19495,19582,19665,19748,19846,19996,20243,20352,21166,21235,21302,22842,23480,23557,23663,23762,23817,23901,23985,24053,24142,24234,24289,24866,24914,24990,25096,25165,25235,25302,25883,26260,26343,26440,26486,26773,26855,26913,26980,27154,27308,27630,27698,28611,28685,28848,28928,29283,29354,29514,30180,30702,30783,37037,37092,37215,37364,37966,39944,40006,40076,40143,40212,40299,40367,41724", "endColumns": "70,79,61,76,85,59,65,51,79,75,80,86,101,86,81,83,84,102,95,68,92,86,78,105,85,92,75,99,77,96,86,82,82,97,75,82,108,101,68,66,661,637,76,105,98,54,83,83,67,88,91,54,79,47,75,105,68,69,66,65,46,82,96,45,47,81,57,66,173,153,128,67,79,73,86,79,81,70,87,67,98,80,89,54,122,48,56,64,61,69,66,68,86,67,50,75", "endOffsets": "14643,14723,14785,14862,14948,15008,15074,15351,15431,15507,15588,15816,15996,16083,16233,16597,16888,17935,18188,18257,18350,18500,18579,18685,18833,19139,19215,19315,19393,19490,19577,19660,19743,19841,19917,20074,20347,20449,21230,21297,21959,23475,23552,23658,23757,23812,23896,23980,24048,24137,24229,24284,24364,24909,24985,25091,25160,25230,25297,25363,25925,26338,26435,26481,26529,26850,26908,26975,27149,27303,27432,27693,27773,28680,28767,28923,29005,29349,29437,29577,30274,30778,30868,37087,37210,37259,37416,38026,40001,40071,40138,40207,40294,40362,40413,41795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,243,327,408,489,620,726,821,916,1032,1213,1413,1564,1651,1752,1845,1941,2104,2210,2310,2556,2668,2882,2981,3041,3104,3192,3290,3388,3493,3701,3780,3847,3921,4003,4088,4161,4240,4309,4382,4499,4586,4698,4794,4867,4954,5034,5121,5181,5287,5392,5503,5633,5698,5790,5889,5961,6061,6134,6225,6297,6409,6527,6616", "endColumns": "81,105,83,80,80,130,105,94,94,115,180,199,150,86,100,92,95,162,105,99,245,111,213,98,59,62,87,97,97,104,207,78,66,73,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,71,99,72,90,71,111,117,88,62", "endOffsets": "132,238,322,403,484,615,721,816,911,1027,1208,1408,1559,1646,1747,1840,1936,2099,2205,2305,2551,2663,2877,2976,3036,3099,3187,3285,3383,3488,3696,3775,3842,3916,3998,4083,4156,4235,4304,4377,4494,4581,4693,4789,4862,4949,5029,5116,5176,5282,5387,5498,5628,5693,5785,5884,5956,6056,6129,6220,6292,6404,6522,6611,6674"}, "to": {"startLines": "171,242,254,255,256,273,284,286,287,337,344,345,346,349,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,406,422,431,432,433,434,435,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15149,20836,22596,22680,22761,24671,25777,25930,26025,30404,30934,31115,31315,31609,31795,31989,32082,32178,32341,32447,32547,32793,33005,33219,33318,33378,33441,33529,33627,33725,33830,34038,34117,34184,34258,34340,34425,34498,34577,34646,34719,35272,35359,35471,35567,35640,35727,35807,35894,35954,36060,36165,36276,36406,36471,36563,36662,37264,39871,40479,40570,40642,40754,40872,41800", "endColumns": "81,105,83,80,80,130,105,94,94,115,180,199,150,86,100,92,95,162,105,99,245,111,213,98,59,62,87,97,97,104,207,78,66,73,81,84,72,78,68,72,116,86,111,95,72,86,79,86,59,105,104,110,129,64,91,98,71,99,72,90,71,111,117,88,62", "endOffsets": "15226,20937,22675,22756,22837,24797,25878,26020,26115,30515,31110,31310,31461,31691,31891,32077,32173,32336,32442,32542,32788,32900,33214,33313,33373,33436,33524,33622,33720,33825,34033,34112,34179,34253,34335,34420,34493,34572,34641,34714,34831,35354,35466,35562,35635,35722,35802,35889,35949,36055,36160,36271,36401,36466,36558,36657,36729,37359,39939,40565,40637,40749,40867,40956,41858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d63aa44a0d7a2e974f4e5d81e07ffd\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "84,465", "startColumns": "4,4", "startOffsets": "8338,43789", "endColumns": "60,78", "endOffsets": "8394,43863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,193,255,338,399,474,531,610,682,745,807", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "128,188,250,333,394,469,526,605,677,740,802,871"}, "to": {"startLines": "178,188,190,191,192,196,204,207,210,214,218,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15656,16458,16602,16664,16747,17028,17568,17758,18025,18355,18690,18982", "endColumns": "77,59,61,82,60,74,56,78,71,62,61,68", "endOffsets": "15729,16513,16659,16742,16803,17098,17620,17832,18092,18413,18747,19046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,13802", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,13877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,239,308,377,440,511", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "135,234,303,372,435,506,573"}, "to": {"startLines": "156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14054,14139,14238,14307,14376,14439,14510", "endColumns": "84,98,68,68,62,70,66", "endOffsets": "14134,14233,14302,14371,14434,14505,14572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fbd7ca05a49499262244ca4fecd9680\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,120", "endOffsets": "159,280"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3007,3116", "endColumns": "108,120", "endOffsets": "3111,3232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7d42b6c2a451039467e7a073b778a81b\\transformed\\preference-1.2.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,341,473,642,725", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "171,258,336,468,637,720,798"}, "to": {"startLines": "71,78,146,150,459,463,464", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7165,7760,13211,13502,43195,43628,43711", "endColumns": "70,86,77,131,168,82,77", "endOffsets": "7231,7842,13284,13629,43359,43706,43784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,341,437,661,706,776,865,937,1152,1212,1293,1358,1413,1477,1835,1909,1975,2028,2084,2158,2280,2391,2448,2524,2600,2685,2757,2869,3196,3278,3355,3417,3477,3537,3598,3665,3741,3840,3933,4033,4133,4227,4300,4379,4469,4680,4904,5025,5169,5225,5988,6049", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,60,60", "endOffsets": "177,336,432,656,701,771,860,932,1147,1207,1288,1353,1408,1472,1830,1904,1970,2023,2079,2153,2275,2386,2443,2519,2595,2680,2752,2864,3191,3273,3350,3412,3472,3532,3593,3660,3736,3835,3928,4028,4128,4222,4295,4374,4464,4675,4899,5020,5164,5220,5983,6044,6105"}, "to": {"startLines": "239,240,241,243,247,248,249,250,251,252,253,269,272,274,282,288,289,296,306,310,311,312,313,314,320,323,328,330,331,332,333,336,338,339,343,347,348,350,352,360,380,381,382,383,384,401,408,409,410,411,413,414,430", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20454,20581,20740,20942,21964,22009,22079,22168,22240,22455,22515,24369,24616,24802,25368,26120,26194,26656,27574,27863,27937,28059,28170,28227,28772,29010,29442,29582,29694,30021,30103,30342,30520,30580,30873,31466,31533,31696,31896,32905,34836,34936,35030,35103,35182,36734,37421,37645,37766,37910,38031,38794,40418", "endColumns": "126,158,95,223,44,69,88,71,214,59,80,64,54,63,357,73,65,52,55,73,121,110,56,75,75,84,71,111,326,81,76,61,59,59,60,66,75,98,92,99,99,93,72,78,89,210,223,120,143,55,762,60,60", "endOffsets": "20576,20735,20831,21161,22004,22074,22163,22235,22450,22510,22591,24429,24666,24861,25721,26189,26255,26704,27625,27932,28054,28165,28222,28298,28843,29090,29509,29689,30016,30098,30175,30399,30575,30635,30929,31528,31604,31790,31984,33000,34931,35025,35098,35177,35267,36940,37640,37761,37905,37961,38789,38850,40474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,196,264,350,423,484,556,619,682,750,818,885,945,1020,1084,1154,1217,1302,1366,1446,1520,1599,1684,1787,1866,1917,1966,2039,2103,2167,2240,2336,2418,2512", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "113,191,259,345,418,479,551,614,677,745,813,880,940,1015,1079,1149,1212,1297,1361,1441,1515,1594,1679,1782,1861,1912,1961,2034,2098,2162,2235,2331,2413,2507,2601"}, "to": {"startLines": "177,180,183,185,186,187,194,195,197,198,199,200,201,202,203,205,206,209,220,221,233,235,236,270,271,283,294,295,297,304,305,315,316,324,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15593,15821,16088,16238,16324,16397,16893,16965,17103,17166,17234,17302,17369,17429,17504,17625,17695,17940,18838,18902,19922,20079,20158,24434,24537,25726,26534,26583,26709,27437,27501,28303,28399,29095,29189", "endColumns": "62,77,67,85,72,60,71,62,62,67,67,66,59,74,63,69,62,84,63,79,73,78,84,102,78,50,48,72,63,63,72,95,81,93,93", "endOffsets": "15651,15894,16151,16319,16392,16453,16960,17023,17161,17229,17297,17364,17424,17499,17563,17690,17753,18020,18897,18977,19991,20153,20238,24532,24611,25772,26578,26651,26768,27496,27569,28394,28476,29184,29278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ebd21074becbab29f0c5ca81c8f4d215\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,460,461,462", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4657,4750,7336,7431,7612,8476,8559,13039,13127,13289,13357,13634,13714,13882,43364,43442,43510", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "4745,4829,7426,7526,7691,8554,8654,13122,13206,13352,13418,13709,13797,13948,43437,43505,43623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\02ee5bfe99bcfa22576a7c61152b85ef\\transformed\\jetified-zxing-android-embedded-3.5.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,116,166,313", "endColumns": "60,49,146,119", "endOffsets": "111,161,308,428"}, "to": {"startLines": "466,467,468,469", "startColumns": "4,4,4,4", "startOffsets": "43868,43929,43979,44126", "endColumns": "60,49,146,119", "endOffsets": "43924,43974,44121,44241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32f45a5bb9d1027885af33ca9e20af1e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5930", "endColumns": "146", "endOffsets": "6072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f7a63276139e16fe7580624d677414a\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4914,5024,5179,5315,5420,5567,5697,5824,6077,6249,6356,6513,6647,6792,6959,7021,7085", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "5019,5174,5310,5415,5562,5692,5819,5925,6244,6351,6508,6642,6787,6954,7016,7080,7160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c72eb4ae29bd4ac7d3f487aebae02cab\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "27778", "endColumns": "84", "endOffsets": "27858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aec03a418f772baa290437dfb60969b4\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3654,3751,3853,3951,4048,4150,4256,13953", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3746,3848,3946,4043,4145,4251,4362,14049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2371f410f7722d632f3a33db7881d70d\\transformed\\jetified-material3-1.0.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,216", "endColumns": "79,80,80", "endOffsets": "130,211,292"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4834,7531,7847", "endColumns": "79,80,80", "endOffsets": "4909,7607,7923"}}]}]}