{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2ac41f25cefd600ae434811e1d648caa\\transformed\\jetified-stripe-core-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,195,259,346,409,481,540,616,686,753,822", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "131,190,254,341,404,476,535,611,681,748,817,884"}, "to": {"startLines": "702,712,714,715,716,720,728,731,734,738,742,746", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66169,66976,67114,67178,67265,67548,68079,68278,68523,68837,69209,69521", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "66245,67030,67173,67260,67323,67615,68133,68349,68588,68899,69273,69583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "182,183,184,185,186,187,188,677", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "18591,18686,18788,18885,18982,19088,19206,64348", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "18681,18783,18880,18977,19083,19201,19316,64444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "245,302,303,304", "startColumns": "4,4,4,4", "startOffsets": "24744,30017,30115,30224", "endColumns": "99,97,108,100", "endOffsets": "24839,30110,30219,30320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ed20371544067e08c9011c623f155332\\transformed\\jetified-payments-core-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,341,427,485,544,600,676,759,839,940,1041,1125,1203,1282,1365,1461,1551,1618,1705,1792,1891,2010,2099,2186,2264,2364,2440,2533,2623,2709,2793,2905,2978,3064,3182,3293,3368,3432,4171,4886,4963,5085,5189,5244,5344,5439,5505,5597,5690,5747,5831,5882,5966,6065,6133,6204,6271,6337,6384,6465,6560,6608,6653,6730,6788,6854,7038,7212,7332,7397,7480,7558,7656,7741,7826,7903,7997,8073,8179,8266,8355,8408,8537,8585,8639,8704,8779,8847,8915,8989,9077,9145,9196", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "119,197,259,336,422,480,539,595,671,754,834,935,1036,1120,1198,1277,1360,1456,1546,1613,1700,1787,1886,2005,2094,2181,2259,2359,2435,2528,2618,2704,2788,2900,2973,3059,3177,3288,3363,3427,4166,4881,4958,5080,5184,5239,5339,5434,5500,5592,5685,5742,5826,5877,5961,6060,6128,6199,6266,6332,6379,6460,6555,6603,6648,6725,6783,6849,7033,7207,7327,7392,7475,7553,7651,7736,7821,7898,7992,8068,8174,8261,8350,8403,8532,8580,8634,8699,8774,8842,8910,8984,9072,9140,9191,9276"}, "to": {"startLines": "687,688,689,690,691,692,693,697,698,699,700,703,705,706,708,713,717,732,735,736,737,739,740,741,743,747,748,749,750,751,752,753,754,755,756,758,761,762,768,769,770,781,782,783,784,785,786,787,788,789,790,791,792,799,800,801,802,803,804,805,809,814,815,816,817,822,823,824,825,826,827,831,832,842,843,845,846,850,851,853,858,865,866,927,928,929,931,936,947,948,949,950,951,952,953,968", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "65072,65141,65219,65281,65358,65444,65502,65810,65866,65942,66025,66250,66432,66533,66684,67035,67328,68354,68593,68683,68750,68904,68991,69090,69278,69588,69675,69753,69853,69929,70022,70112,70198,70282,70394,70542,70802,70920,71764,71839,71903,73599,74314,74391,74513,74617,74672,74772,74867,74933,75025,75118,75175,75770,75821,75905,76004,76072,76143,76210,76732,77117,77198,77293,77341,77626,77703,77761,77827,78011,78185,78493,78558,79523,79601,79780,79865,80231,80308,80481,81132,81663,81750,88156,88209,88338,88497,89095,91120,91195,91263,91331,91405,91493,91561,92896", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "65136,65214,65276,65353,65439,65497,65556,65861,65937,66020,66100,66346,66528,66612,66757,67109,67406,68445,68678,68745,68832,68986,69085,69204,69362,69670,69748,69848,69924,70017,70107,70193,70277,70389,70462,70623,70915,71026,71834,71898,72637,74309,74386,74508,74612,74667,74767,74862,74928,75020,75113,75170,75254,75816,75900,75999,76067,76138,76205,76271,76774,77193,77288,77336,77381,77698,77756,77822,78006,78180,78300,78553,78636,79596,79694,79860,79945,80303,80397,80552,81233,81745,81834,88204,88333,88381,88546,89155,91190,91258,91326,91400,91488,91556,91607,92976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "328,1099", "startColumns": "4,4", "startOffsets": "32197,105090", "endColumns": "60,77", "endOffsets": "32253,105163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "205,206,254,255,264,348,349,531,539,545,549,648,649,672,986,987,997", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20757,20847,25881,25979,26685,33429,33512,52205,52792,53488,53790,62151,62233,64024,94606,94684,95460", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "20842,20924,25974,26074,26766,33507,33598,52287,52872,53553,53852,62228,62311,64091,94679,94745,95574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\22bbc9bab209556a742f74d50540c114\\transformed\\jetified-uikit-1.31.1-SANDBOX\\res\\values-in\\values-in.xml", "from": {"startLines": "2,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,347,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,493,575,648,729,810,887,947,1036,1095,1139,1286,1454,1609,1736,1782,1987,2151,2251,2422,2547,2697,2960,3160,3319,3414,3580,3647,3689,3775,3858,3898,3974,4055,4276,4417,4537,4592,4647,4695,4780,4879,4966,5029,5085,5133,5183,5282,5355,5446,5571,5674,5836,5885,5973,6074,6212,6423,6574,6653,6808,7138,7424,7727,7895,8146,8246,8366,8472,8610,8708,8794,8929,8980,9041,9135,9208,9290,9370,9505,9659,9832,9905,10069,10177,10305,10427,10554,10698,10883,11038,11112,11209,11285,11371,11458,11562,11676,11775,11849,11920,11989,12107,12309,12416,12580,12707,12823,13005,13129,13224,13325,13409,13493,13704,13823,13956,14085,14185,14267,14485,14595,14718,14763,14807,14852,14913,14987,15054,15158,15244,15303,15357,15442,15505,15591,15657,15709,15880,16008,16118,16174,16231,16324,16398,16488,16583,16672,16769,16884,17034,17100,17163,17248,17339,17414,17453,17518,17586,17654,17722,17781,17851,17989,18136,18304,18450,18691,18732,18793,18923,19020,19108,19149,19225,19328,19399,19482,19574,19625,19668,19739,19823,19974,20078,20162,20238,20320,20465,20601,20733,20814,20904,21018,21145,21217,21287,21360,21435,21516,21608,21685,21767,21841,21922,21997,22068,22148,22212,22281,22342,22417,22457,22519,22585,22673,22735,22820,22908,23020,23123,23201,23283,23401,23506,23616,23701,23790,23910,23976,24020,24068,24132,24197,24240,24288,24329,24374,24446,24513,24569,24639,24714,24795,24857,24916,24994,25045,25126,25174,25325,25570,25741,25897,25990,26210,26272,26361,26452,26564,26653,26802,26875,26949,27079,27126,27173,27220,27309,27395,27445,27540,27621,27694,27764,27845,27932,28043,28176,28373,28528,28652,28753,28956,29058,29134,29219,29371,29511,29711,29814,30020,30086,30199,30326,30441,30540,30782,30878,30948,31026,31138,31238,31432,31553,31753,31800,31842,31916,32064,32280,32414,32555,32629,32716,32848,32960,33174,33354,33454,33709,33752,33810,33897,34036,34127,34239,34409,34590,34761,34832,34895,34958,35100,35206,35322,35473,35624,35775,35925,35987,36054,36150,36226,36308,36429,36545,36711,36891,37028,37181,37308,37425,37513,37605,37731,37874,38005,38200,38340,38477,38559,38689,38791,38865,38964,39109,39183,39224,39266,39327,39372,39430,39549,39606,39695,39753,39827,39933,40112,40245,40358,40430,40545,40590,40635,40704,40763,40821,40872,40922,40995,41082,41165,41245,41333,41408,41491,41552,41608,41662,41760,41858,41956,42062,42149,42255,42331,42405,42462,42542,42616,42684,42745,42820,42883,42950,43008,43075,43149,43248,43363,43472,43564,43666,43778,43881,43989,44098,44214,44307,44389,44480,44594,44680,44782,44868,44963,45076,45201,45298,45420,45529,45621,45707,45762,45839,45902,45979,46036,46105,46182,46272,46332,46404,46489,46577,46707,46771,46838,46913,46995,47079,47179,47237,47340,47419,47506,47590,47727,47896,48005,48095,48199,48291,48338,48419,48481,48553,48620,48668,48731,48801,48933,49006,49069,49218,49287,49355,49435,49497,49575,49616,49667,49796,49915,49974,50097,50148,50189,50231,50291,50354,50431,50503,50543,50614,50673,50763,50843,50896,50964,51025,51104,51166,51210,51258,51306,51358,51420,51478,51538,51596,51729,51865,51968,52021,52120,52187,52255,52329,52375,52417,52479,52524,52615,52655,52707,52806,52872,52939,53012,53110,53176,53295,53426,53723,53956,54084,54196,54283,54355,54431,54491,54551,54599,54659,54732,54858,55019,55180,55277,55403,55517,55704,55792,55846,55918,55994,56092,56164,56263,56362,56484,56601,56689,56781,56910,57025,57145,57240,57339,57415,57532,57634,57754,57883,57991,58132,58234,58340,58459,58582,58661,58717,58781,58834,58928,59014,59102,59195,59290,59385,59476,59549,59635,59745,59822,59902,59967,60060,60155,60238,60344,60455,60532,60622,60736,60824,60907,60995,61085,61188,61245,61315,61368,61473,61566,61647,61730,61807,61899", "endLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,346,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651", "endColumns": "19,81,72,80,80,76,59,88,58,43,146,167,154,126,45,204,163,99,170,124,149,262,199,158,94,165,66,41,85,82,39,75,80,112,140,119,54,54,47,84,98,86,62,55,47,49,98,72,90,124,102,161,48,87,100,137,210,150,78,154,329,285,302,167,250,99,119,105,137,97,85,134,50,60,93,72,81,79,134,153,172,72,163,107,127,121,126,143,184,154,73,96,75,85,86,103,113,98,73,70,68,117,201,106,163,126,115,181,123,94,100,83,83,210,118,132,128,99,81,217,109,122,44,43,44,60,73,66,103,85,58,53,84,62,85,65,51,170,127,109,55,56,92,73,89,94,88,96,114,149,65,62,84,90,74,38,64,67,67,67,58,69,137,146,167,145,240,40,60,129,96,87,40,75,102,70,82,91,50,42,70,83,150,103,83,75,81,144,135,131,80,89,113,126,71,69,72,74,80,91,76,81,73,80,74,70,79,63,68,60,74,39,61,65,87,61,84,87,111,102,77,81,117,104,109,84,88,119,65,43,47,63,64,42,47,40,44,71,66,55,69,74,80,61,58,77,50,80,47,150,123,170,155,92,219,61,88,90,111,88,148,72,73,129,46,46,46,88,85,49,94,80,72,69,80,86,110,132,196,154,123,100,202,101,75,84,151,139,199,102,205,65,112,126,114,98,241,95,69,77,111,99,193,120,199,46,41,73,147,215,133,140,73,86,131,111,213,179,99,254,42,57,86,138,90,111,169,57,78,70,62,62,141,105,115,150,150,150,149,61,66,95,75,81,120,115,165,179,136,152,126,116,87,91,125,142,130,194,139,136,81,129,101,73,98,144,73,40,41,60,44,57,118,56,88,57,73,105,178,132,112,71,114,44,44,68,58,57,50,49,72,86,82,79,87,74,82,60,55,53,97,97,97,105,86,105,75,73,56,79,73,67,60,74,62,66,57,66,73,98,114,108,91,101,111,102,107,108,115,92,81,90,113,85,101,85,94,112,124,96,121,108,91,85,54,76,62,76,56,68,76,89,59,71,84,87,129,63,66,74,81,83,99,57,102,78,86,83,136,168,108,89,103,91,46,80,61,71,66,47,62,69,131,72,62,148,68,67,79,61,77,40,50,128,118,58,122,50,40,41,59,62,76,71,39,70,58,89,79,52,67,60,78,61,43,47,47,51,61,57,59,57,132,135,102,52,98,66,67,73,45,41,61,44,90,39,51,98,65,66,72,97,65,118,130,296,232,127,111,86,71,75,59,59,47,59,72,125,160,160,96,125,113,186,87,53,71,75,97,71,98,98,121,116,87,91,128,114,119,94,98,75,116,101,119,128,107,140,101,105,118,122,78,55,63,52,93,85,87,92,94,94,90,72,85,109,76,79,64,92,94,82,105,110,76,89,113,87,82,87,89,102,56,69,52,104,92,80,82,76,91,86", "endOffsets": "488,570,643,724,805,882,942,1031,1090,1134,1281,1449,1604,1731,1777,1982,2146,2246,2417,2542,2692,2955,3155,3314,3409,3575,3642,3684,3770,3853,3893,3969,4050,4271,4412,4532,4587,4642,4690,4775,4874,4961,5024,5080,5128,5178,5277,5350,5441,5566,5669,5831,5880,5968,6069,6207,6418,6569,6648,6803,7133,7419,7722,7890,8141,8241,8361,8467,8605,8703,8789,8924,8975,9036,9130,9203,9285,9365,9500,9654,9827,9900,10064,10172,10300,10422,10549,10693,10878,11033,11107,11204,11280,11366,11453,11557,11671,11770,11844,11915,11984,12102,12304,12411,12575,12702,12818,13000,13124,13219,13320,13404,13488,13699,13818,13951,14080,14180,14262,14480,14590,14713,14758,14802,14847,14908,14982,15049,15153,15239,15298,15352,15437,15500,15586,15652,15704,15875,16003,16113,16169,16226,16319,16393,16483,16578,16667,16764,16879,17029,17095,17158,17243,17334,17409,17448,17513,17581,17649,17717,17776,17846,17984,18131,18299,18445,18686,18727,18788,18918,19015,19103,19144,19220,19323,19394,19477,19569,19620,19663,19734,19818,19969,20073,20157,20233,20315,20460,20596,20728,20809,20899,21013,21140,21212,21282,21355,21430,21511,21603,21680,21762,21836,21917,21992,22063,22143,22207,22276,22337,22412,22452,22514,22580,22668,22730,22815,22903,23015,23118,23196,23278,23396,23501,23611,23696,23785,23905,23971,24015,24063,24127,24192,24235,24283,24324,24369,24441,24508,24564,24634,24709,24790,24852,24911,24989,25040,25121,25169,25320,25565,25736,25892,25985,26205,26267,26356,26447,26559,26648,26797,26870,26944,27074,27121,27168,27215,27304,27390,27440,27535,27616,27689,27759,27840,27927,28038,28171,28368,28523,28647,28748,28951,29053,29129,29214,29366,29506,29706,29809,30015,30081,30194,30321,30436,30535,30777,30873,30943,31021,31133,31233,31427,31548,31748,31795,31837,31911,32059,32275,32409,32550,32624,32711,32843,32955,33169,33349,33449,33704,33747,33805,33892,34031,34122,34234,34404,34585,34756,34827,34890,34953,35095,35201,35317,35468,35619,35770,35920,35982,36049,36145,36221,36303,36424,36540,36706,36886,37023,37176,37303,37420,37508,37600,37726,37869,38000,38195,38335,38472,38554,38684,38786,38860,38959,39104,39178,39219,39261,39322,39367,39425,39544,39601,39690,39748,39822,39928,40107,40240,40353,40425,40540,40585,40630,40699,40758,40816,40867,40917,40990,41077,41160,41240,41328,41403,41486,41547,41603,41657,41755,41853,41951,42057,42144,42250,42326,42400,42457,42537,42611,42679,42740,42815,42878,42945,43003,43070,43144,43243,43358,43467,43559,43661,43773,43876,43984,44093,44209,44302,44384,44475,44589,44675,44777,44863,44958,45071,45196,45293,45415,45524,45616,45702,45757,45834,45897,45974,46031,46100,46177,46267,46327,46399,46484,46572,46702,46766,46833,46908,46990,47074,47174,47232,47335,47414,47501,47585,47722,47891,48000,48090,48194,48286,48333,48414,48476,48548,48615,48663,48726,48796,48928,49001,49064,49213,49282,49350,49430,49492,49570,49611,49662,49791,49910,49969,50092,50143,50184,50226,50286,50349,50426,50498,50538,50609,50668,50758,50838,50891,50959,51020,51099,51161,51205,51253,51301,51353,51415,51473,51533,51591,51724,51860,51963,52016,52115,52182,52250,52324,52370,52412,52474,52519,52610,52650,52702,52801,52867,52934,53007,53105,53171,53290,53421,53718,53951,54079,54191,54278,54350,54426,54486,54546,54594,54654,54727,54853,55014,55175,55272,55398,55512,55699,55787,55841,55913,55989,56087,56159,56258,56357,56479,56596,56684,56776,56905,57020,57140,57235,57334,57410,57527,57629,57749,57878,57986,58127,58229,58335,58454,58577,58656,58712,58776,58829,58923,59009,59097,59190,59285,59380,59471,59544,59630,59740,59817,59897,59962,60055,60150,60233,60339,60450,60527,60617,60731,60819,60902,60990,61080,61183,61240,61310,61363,61468,61561,61642,61725,61802,61894,61981"}, "to": {"startLines": "2,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,78,79,80,81,82,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,189,190,191,192,193,194,195,198,199,200,201,202,203,208,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,246,247,248,249,250,251,252,253,256,257,258,259,260,262,263,265,266,267,268,269,270,271,272,273,274,275,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,295,296,297,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,350,351,352,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,433,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,530,532,533,534,535,536,537,538,541,542,543,544,546,547,548,550,551,552,553,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,639,640,641,642,643,644,645,646,647,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,669,670,671,673,674,675,676,678,679,680,681,682,683,684,685,686,983,984,988,989,990,991,992,993,994,995,996,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1100,1101,1102,1103,1104,1105,1106,1107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3461,3543,3616,3697,3778,3855,3915,4004,4063,4107,4254,4422,4577,4704,4750,4955,5119,5219,5390,5515,5665,5928,6128,6287,6382,6548,6615,6657,6970,7053,7093,7169,7250,7471,7612,7732,7787,7842,7890,7975,8074,8161,8224,8280,8328,8378,8477,8550,8641,8766,8869,9031,9080,9168,9269,9407,9618,9769,9848,10003,10333,10619,10922,11090,11341,11441,11561,11667,11805,11903,11989,12124,12175,12236,12330,12403,12485,12565,12700,12854,13027,13100,13264,13372,13500,13622,13749,13893,14078,14233,14307,14404,14480,14566,14653,14757,15280,15379,15453,15524,15593,15711,15913,16020,16184,16311,16427,16609,16733,16828,16929,17013,17097,17308,17427,17560,17689,17789,17871,18089,18199,18322,18367,18411,18456,18517,19321,19388,19492,19578,19637,19691,19776,20061,20147,20213,20265,20436,20564,21007,23264,23321,23414,23488,23578,23673,23762,23859,23974,24124,24190,24253,24338,24429,24504,24543,24608,24676,24844,24912,24971,25041,25179,25326,25494,25640,26079,26120,26181,26311,26408,26568,26609,26771,26874,26945,27028,27120,27171,27214,27285,27369,27520,27624,27773,27849,27931,28076,28212,28344,28425,28515,28629,28756,28828,28898,28971,29046,29127,29219,29296,29465,29539,29620,29866,29937,30325,30389,30458,30519,30594,30634,30696,30762,30850,30912,30997,31085,31197,31300,31378,31460,31578,31683,31793,31878,31967,32087,32153,32258,32306,32370,32435,32478,32526,32567,32612,32684,32751,32807,32877,32952,33033,33095,33154,33232,33283,33603,33651,33802,34047,34218,34374,34467,34687,34749,34838,34929,35041,35130,35279,35352,35426,35556,35603,35650,35697,35786,35872,35922,36017,36098,36171,36241,36322,36409,36520,36653,36850,37005,37129,37230,37433,37535,37611,37696,37848,37988,38188,38291,38497,38563,38676,38803,38918,39017,39259,39355,39425,39503,39615,39715,39909,40030,40230,40336,40378,40452,40600,40816,40950,41091,41165,41252,41384,41496,41710,41890,41990,42245,42288,42346,42433,42572,42663,42775,42945,43126,43297,43368,43431,43494,43636,43742,43858,44009,44160,44311,44461,44523,44590,44686,44762,44844,44965,45081,45247,45427,45564,45717,45844,46896,46984,47076,47202,47345,47476,47671,47811,47948,48030,48160,48262,48336,48435,48580,48654,48695,52144,52292,52337,52395,52514,52571,52660,52718,52957,53063,53242,53375,53558,53630,53745,53857,53902,53971,54030,54168,54219,54269,54342,54429,54512,54592,54680,54755,54838,54899,54955,55009,55107,55205,55303,55409,55496,55602,55678,55752,55809,55889,55963,56031,56092,56167,56230,56297,56355,56422,56496,56595,56710,56819,56911,57013,57125,57228,57336,57445,57561,57654,57736,57827,57941,58027,58129,58215,58310,58423,58548,58645,58767,58876,58968,59054,59109,59186,59249,59326,59383,59452,59529,59619,59679,59751,59836,59924,60054,60118,60185,60260,60342,60426,60526,60584,60687,60766,60853,60937,61074,61243,61488,61578,61682,61774,61821,61902,61964,62036,62103,62316,62379,62449,62581,62654,62717,62866,62935,63003,63083,63145,63223,63264,63315,63444,63563,63622,63745,63881,63922,63964,64096,64159,64236,64308,64449,64520,64579,64669,64749,64802,64870,64931,65010,94345,94389,94750,94798,94850,94912,94970,95030,95088,95221,95357,95579,95632,95731,95798,95866,95940,95986,96028,96090,96135,96226,96266,96318,96417,96483,96550,96623,96721,96787,96906,97037,97334,97567,97695,97807,97894,97966,98042,98102,98162,98210,98270,98343,98469,98630,98791,98888,99014,99128,99315,99403,99457,99529,99605,99703,99775,99874,99973,100095,100212,100300,100392,100521,100636,100756,100851,100950,101026,101143,101245,101365,101494,101602,101743,101845,101951,102070,102193,102272,102492,102556,102609,102703,102789,102877,102970,103065,103160,103251,103324,103410,103520,103597,103677,103742,103835,103930,104013,104119,104230,104307,104397,104511,104599,104682,104770,104860,104963,105020,105168,105221,105326,105419,105500,105583,105660,105752", "endLines": "16,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,78,79,80,81,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,189,190,191,192,193,194,195,198,199,200,201,202,203,208,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,246,247,248,249,250,251,252,253,256,257,258,259,260,262,263,265,266,267,268,269,270,271,272,273,274,275,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,295,296,297,300,301,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,350,351,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,432,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,530,532,533,534,535,536,537,538,541,542,543,544,546,547,548,550,551,552,553,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,639,640,641,642,643,644,645,646,647,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,669,670,671,673,674,675,676,678,679,680,681,682,683,684,685,686,983,984,988,989,990,991,992,993,994,995,996,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1100,1101,1102,1103,1104,1105,1106,1107", "endColumns": "19,81,72,80,80,76,59,88,58,43,146,167,154,126,45,204,163,99,170,124,149,262,199,158,94,165,66,41,85,82,39,75,80,112,140,119,54,54,47,84,98,86,62,55,47,49,98,72,90,124,102,161,48,87,100,137,210,150,78,154,329,285,302,167,250,99,119,105,137,97,85,134,50,60,93,72,81,79,134,153,172,72,163,107,127,121,126,143,184,154,73,96,75,85,86,103,113,98,73,70,68,117,201,106,163,126,115,181,123,94,100,83,83,210,118,132,128,99,81,217,109,122,44,43,44,60,73,66,103,85,58,53,84,62,85,65,51,170,127,109,55,56,92,73,89,94,88,96,114,149,65,62,84,90,74,38,64,67,67,67,58,69,137,146,167,145,240,40,60,129,96,87,40,75,102,70,82,91,50,42,70,83,150,103,83,75,81,144,135,131,80,89,113,126,71,69,72,74,80,91,76,81,73,80,74,70,79,63,68,60,74,39,61,65,87,61,84,87,111,102,77,81,117,104,109,84,88,119,65,43,47,63,64,42,47,40,44,71,66,55,69,74,80,61,58,77,50,80,47,150,123,170,155,92,219,61,88,90,111,88,148,72,73,129,46,46,46,88,85,49,94,80,72,69,80,86,110,132,196,154,123,100,202,101,75,84,151,139,199,102,205,65,112,126,114,98,241,95,69,77,111,99,193,120,199,46,41,73,147,215,133,140,73,86,131,111,213,179,99,254,42,57,86,138,90,111,169,57,78,70,62,62,141,105,115,150,150,150,149,61,66,95,75,81,120,115,165,179,136,152,126,116,87,91,125,142,130,194,139,136,81,129,101,73,98,144,73,40,41,60,44,57,118,56,88,57,73,105,178,132,112,71,114,44,44,68,58,57,50,49,72,86,82,79,87,74,82,60,55,53,97,97,97,105,86,105,75,73,56,79,73,67,60,74,62,66,57,66,73,98,114,108,91,101,111,102,107,108,115,92,81,90,113,85,101,85,94,112,124,96,121,108,91,85,54,76,62,76,56,68,76,89,59,71,84,87,129,63,66,74,81,83,99,57,102,78,86,83,136,168,108,89,103,91,46,80,61,71,66,47,62,69,131,72,62,148,68,67,79,61,77,40,50,128,118,58,122,50,40,41,59,62,76,71,39,70,58,89,79,52,67,60,78,61,43,47,47,51,61,57,59,57,132,135,102,52,98,66,67,73,45,41,61,44,90,39,51,98,65,66,72,97,65,118,130,296,232,127,111,86,71,75,59,59,47,59,72,125,160,160,96,125,113,186,87,53,71,75,97,71,98,98,121,116,87,91,128,114,119,94,98,75,116,101,119,128,107,140,101,105,118,122,78,55,63,52,93,85,87,92,94,94,90,72,85,109,76,79,64,92,94,82,105,110,76,89,113,87,82,87,89,102,56,69,52,104,92,80,82,76,91,86", "endOffsets": "583,3538,3611,3692,3773,3850,3910,3999,4058,4102,4249,4417,4572,4699,4745,4950,5114,5214,5385,5510,5660,5923,6123,6282,6377,6543,6610,6652,6738,7048,7088,7164,7245,7466,7607,7727,7782,7837,7885,7970,8069,8156,8219,8275,8323,8373,8472,8545,8636,8761,8864,9026,9075,9163,9264,9402,9613,9764,9843,9998,10328,10614,10917,11085,11336,11436,11556,11662,11800,11898,11984,12119,12170,12231,12325,12398,12480,12560,12695,12849,13022,13095,13259,13367,13495,13617,13744,13888,14073,14228,14302,14399,14475,14561,14648,14752,14866,15374,15448,15519,15588,15706,15908,16015,16179,16306,16422,16604,16728,16823,16924,17008,17092,17303,17422,17555,17684,17784,17866,18084,18194,18317,18362,18406,18451,18512,18586,19383,19487,19573,19632,19686,19771,19834,20142,20208,20260,20431,20559,20669,21058,23316,23409,23483,23573,23668,23757,23854,23969,24119,24185,24248,24333,24424,24499,24538,24603,24671,24739,24907,24966,25036,25174,25321,25489,25635,25876,26115,26176,26306,26403,26491,26604,26680,26869,26940,27023,27115,27166,27209,27280,27364,27515,27619,27703,27844,27926,28071,28207,28339,28420,28510,28624,28751,28823,28893,28966,29041,29122,29214,29291,29373,29534,29615,29690,29932,30012,30384,30453,30514,30589,30629,30691,30757,30845,30907,30992,31080,31192,31295,31373,31455,31573,31678,31788,31873,31962,32082,32148,32192,32301,32365,32430,32473,32521,32562,32607,32679,32746,32802,32872,32947,33028,33090,33149,33227,33278,33359,33646,33797,34042,34213,34369,34462,34682,34744,34833,34924,35036,35125,35274,35347,35421,35551,35598,35645,35692,35781,35867,35917,36012,36093,36166,36236,36317,36404,36515,36648,36845,37000,37124,37225,37428,37530,37606,37691,37843,37983,38183,38286,38492,38558,38671,38798,38913,39012,39254,39350,39420,39498,39610,39710,39904,40025,40225,40272,40373,40447,40595,40811,40945,41086,41160,41247,41379,41491,41705,41885,41985,42240,42283,42341,42428,42567,42658,42770,42940,43121,43292,43363,43426,43489,43631,43737,43853,44004,44155,44306,44456,44518,44585,44681,44757,44839,44960,45076,45242,45422,45559,45712,45839,45956,46979,47071,47197,47340,47471,47666,47806,47943,48025,48155,48257,48331,48430,48575,48649,48690,48732,52200,52332,52390,52509,52566,52655,52713,52787,53058,53237,53370,53483,53625,53740,53785,53897,53966,54025,54083,54214,54264,54337,54424,54507,54587,54675,54750,54833,54894,54950,55004,55102,55200,55298,55404,55491,55597,55673,55747,55804,55884,55958,56026,56087,56162,56225,56292,56350,56417,56491,56590,56705,56814,56906,57008,57120,57223,57331,57440,57556,57649,57731,57822,57936,58022,58124,58210,58305,58418,58543,58640,58762,58871,58963,59049,59104,59181,59244,59321,59378,59447,59524,59614,59674,59746,59831,59919,60049,60113,60180,60255,60337,60421,60521,60579,60682,60761,60848,60932,61069,61238,61347,61573,61677,61769,61816,61897,61959,62031,62098,62146,62374,62444,62576,62649,62712,62861,62930,62998,63078,63140,63218,63259,63310,63439,63558,63617,63740,63791,63917,63959,64019,64154,64231,64303,64343,64515,64574,64664,64744,64797,64865,64926,65005,65067,94384,94432,94793,94845,94907,94965,95025,95083,95216,95352,95455,95627,95726,95793,95861,95935,95981,96023,96085,96130,96221,96261,96313,96412,96478,96545,96618,96716,96782,96901,97032,97329,97562,97690,97802,97889,97961,98037,98097,98157,98205,98265,98338,98464,98625,98786,98883,99009,99123,99310,99398,99452,99524,99600,99698,99770,99869,99968,100090,100207,100295,100387,100516,100631,100751,100846,100945,101021,101138,101240,101360,101489,101597,101738,101840,101946,102065,102188,102267,102323,102551,102604,102698,102784,102872,102965,103060,103155,103246,103319,103405,103515,103592,103672,103737,103830,103925,104008,104114,104225,104302,104392,104506,104594,104677,104765,104855,104958,105015,105085,105216,105321,105414,105495,105578,105655,105747,105834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "76,77", "startColumns": "4,4", "startOffsets": "6743,6852", "endColumns": "108,117", "endOffsets": "6847,6965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b7797762919ee15e445780966e20543\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,344,447,688,736,806,907,980,1240,1300,1391,1457,1512,1576,1885,1957,2024,2077,2130,2220,2353,2461,2518,2604,2685,2771,2850,2956,3269,3348,3425,3489,3549,3611,3671,3744,3826,3926,4021,4120,4220,4314,4388,4467,4552,4781,5019,5135,5267,5325,6079,6141", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,61,64", "endOffsets": "170,339,442,683,731,801,902,975,1235,1295,1386,1452,1507,1571,1880,1952,2019,2072,2125,2215,2348,2456,2513,2599,2680,2766,2845,2951,3264,3343,3420,3484,3544,3606,3666,3739,3821,3921,4016,4115,4215,4309,4383,4462,4547,4776,5014,5130,5262,5320,6074,6136,6201"}, "to": {"startLines": "763,764,765,767,771,772,773,774,775,776,777,793,796,798,806,812,813,820,830,834,835,836,837,838,844,847,852,854,855,856,857,860,862,863,867,871,872,874,876,884,904,905,906,907,908,925,932,933,934,935,937,938,954", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "71031,71151,71320,71523,72642,72690,72760,72861,72934,73194,73254,75259,75508,75706,76276,76978,77050,77509,78440,78738,78828,78961,79069,79126,79699,79950,80402,80557,80663,80976,81055,81298,81474,81534,81839,82474,82547,82721,82930,83982,85960,86060,86154,86228,86307,87835,88551,88789,88905,89037,89160,89914,91612", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,61,64", "endOffsets": "71146,71315,71418,71759,72685,72755,72856,72929,73189,73249,73340,75320,75558,75765,76580,77045,77112,77557,78488,78823,78956,79064,79121,79207,79775,80031,80476,80658,80971,81050,81127,81357,81529,81591,81894,82542,82624,82816,83020,84076,86055,86149,86223,86302,86387,88059,88784,88900,89032,89090,89909,89971,91672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,668", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "757,872,976,1081,1168,1272,1388,1471,1549,1640,1733,1828,1922,2022,2115,2210,2304,2395,2486,2572,2675,2780,2881,2985,3094,3202,3362,63796", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "867,971,1076,1163,1267,1383,1466,1544,1635,1728,1823,1917,2017,2110,2205,2299,2390,2481,2567,2670,2775,2876,2980,3089,3197,3357,3456,63876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "209,210,211,212,213,214,215,216,218,219,220,221,222,223,224,225,226", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21063,21170,21334,21460,21566,21721,21848,21963,22201,22367,22472,22636,22762,22917,23061,23125,23185", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "21165,21329,21455,21561,21716,21843,21958,22064,22362,22467,22631,22757,22912,23056,23120,23180,23259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f2da40362deff4591e4d0e8735fd0a8f\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,246,337,419,500,643,743,849,942,1054,1264,1496,1629,1721,1830,1923,2022,2200,2308,2401,2679,2787,3004,3098,3158,3228,3316,3417,3513,3619,3855,3921,3989,4067,4151,4240,4308,4389,4460,4536,4666,4753,4857,4951,5023,5102,5169,5257,5317,5421,5541,5648,5777,5840,5934,6038,6109,6220,6294,6384,6457,6575,6692,6777", "endColumns": "90,99,90,81,80,142,99,105,92,111,209,231,132,91,108,92,98,177,107,92,277,107,216,93,59,69,87,100,95,105,235,65,67,77,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,70,110,73,89,72,117,116,84,63", "endOffsets": "141,241,332,414,495,638,738,844,937,1049,1259,1491,1624,1716,1825,1918,2017,2195,2303,2396,2674,2782,2999,3093,3153,3223,3311,3412,3508,3614,3850,3916,3984,4062,4146,4235,4303,4384,4455,4531,4661,4748,4852,4946,5018,5097,5164,5252,5312,5416,5536,5643,5772,5835,5929,6033,6104,6215,6289,6379,6452,6570,6687,6772,6836"}, "to": {"startLines": "695,766,778,779,780,797,808,810,811,861,868,869,870,873,875,877,878,879,880,881,882,883,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,930,946,955,956,957,958,959,969", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "65637,71423,73345,73436,73518,75563,76632,76779,76885,81362,81899,82109,82341,82629,82821,83025,83118,83217,83395,83503,83596,83874,84081,84298,84392,84452,84522,84610,84711,84807,84913,85149,85215,85283,85361,85445,85534,85602,85683,85754,85830,86392,86479,86583,86677,86749,86828,86895,86983,87043,87147,87267,87374,87503,87566,87660,87764,88386,91046,91677,91767,91840,91958,92075,92981", "endColumns": "90,99,90,81,80,142,99,105,92,111,209,231,132,91,108,92,98,177,107,92,277,107,216,93,59,69,87,100,95,105,235,65,67,77,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,70,110,73,89,72,117,116,84,63", "endOffsets": "65723,71518,73431,73513,73594,75701,76727,76880,76973,81469,82104,82336,82469,82716,82925,83113,83212,83390,83498,83591,83869,83977,84293,84387,84447,84517,84605,84706,84802,84908,85144,85210,85278,85356,85440,85529,85597,85678,85749,85825,85955,86474,86578,86672,86744,86823,86890,86978,87038,87142,87262,87369,87498,87561,87655,87759,87830,88492,91115,91762,91835,91953,92070,92155,93040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2807,2897,2979,3032,3084,3150,3222,3306,3389,3469,3544,3620,3693,3768,3866,3951,4026,4118,4212,4286,4359,4453,4505,4587,4656,4741,4828,4890,4954,5017,5089,5192,5297,5392,5495,5552,5608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,134,89,81,52,51,65,71,83,82,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2802,2892,2974,3027,3079,3145,3217,3301,3384,3464,3539,3615,3688,3763,3861,3946,4021,4113,4207,4281,4354,4448,4500,4582,4651,4736,4823,4885,4949,5012,5084,5187,5292,5387,5490,5547,5603,5683"}, "to": {"startLines": "17,147,148,149,150,151,196,197,204,276,299,347,409,458,459,460,461,462,463,464,465,466,467,468,469,470,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,554", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "588,14871,14950,15026,15105,15195,19839,19945,20674,27708,29772,33364,40277,45961,46048,46110,46172,46232,46298,46360,46414,46526,46583,46644,46698,46770,48737,48823,48907,49046,49127,49208,49343,49433,49515,49568,49620,49686,49758,49842,49925,50005,50080,50156,50229,50304,50402,50487,50562,50654,50748,50822,50895,50989,51041,51123,51192,51277,51364,51426,51490,51553,51625,51728,51833,51928,52031,52088,54088", "endLines": "20,147,148,149,150,151,196,197,204,276,299,347,409,458,459,460,461,462,463,464,465,466,467,468,469,470,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,554", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,134,89,81,52,51,65,71,83,82,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79", "endOffsets": "752,14945,15021,15100,15190,15275,19940,20056,20752,27768,29861,33424,40331,46043,46105,46167,46227,46293,46355,46409,46521,46578,46639,46693,46765,46891,48818,48902,49041,49122,49203,49338,49428,49510,49563,49615,49681,49753,49837,49920,50000,50075,50151,50224,50299,50397,50482,50557,50649,50743,50817,50890,50984,51036,51118,51187,51272,51359,51421,51485,51548,51620,51723,51828,51923,52026,52083,52139,54163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,205", "endColumns": "77,71,76", "endOffsets": "128,200,277"}, "to": {"startLines": "207,261,298", "startColumns": "4,4,4", "startOffsets": "20929,26496,29695", "endColumns": "77,71,76", "endOffsets": "21002,26563,29767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24101954538a7f7c2c85ceec583f6b9b\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,351,420,481,552,618,680,748,818,878,940,1013,1077,1154,1217,1290,1353,1444,1519,1601,1693,1791,1876,1923,1970,2046,2110,2176,2245,2349,2436,2534", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "114,195,262,346,415,476,547,613,675,743,813,873,935,1008,1072,1149,1212,1285,1348,1439,1514,1596,1688,1786,1871,1918,1965,2041,2105,2171,2240,2344,2431,2529,2626"}, "to": {"startLines": "701,704,707,709,710,711,718,719,721,722,723,724,725,726,727,729,730,733,744,745,757,759,760,794,795,807,818,819,821,828,829,839,840,848,849", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66105,66351,66617,66762,66846,66915,67411,67482,67620,67682,67750,67820,67880,67942,68015,68138,68215,68450,69367,69430,70467,70628,70710,75325,75423,76585,77386,77433,77562,78305,78371,79212,79316,80036,80134", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "66164,66427,66679,66841,66910,66971,67477,67543,67677,67745,67815,67875,67937,68010,68074,68210,68273,68518,69425,69516,70537,70705,70797,75418,75503,76627,77428,77504,77621,78366,78435,79311,79398,80129,80226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "833", "startColumns": "4", "startOffsets": "78641", "endColumns": "96", "endOffsets": "78733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "174,261,341,477,646,731", "endColumns": "86,79,135,168,84,78", "endOffsets": "256,336,472,641,726,805"}, "to": {"startLines": "294,540,638,985,1067,1068", "startColumns": "4,4,4,4,4,4", "startOffsets": "29378,52877,61352,94437,102328,102413", "endColumns": "86,79,135,168,84,78", "endOffsets": "29460,52952,61483,94601,102408,102487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7411434f15fbcff9befcd2c1cf22cdcf\\transformed\\jetified-link-20.52.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,213,333,393,460,552,612,680,758,862,1040,1318,1622,1694,1765,1850,1936,2075,2218,2286,2358,2546,2617,2675,2750,2831,2949,3068,3173,3260,3338,3427,3496", "endColumns": "75,81,119,59,66,91,59,67,77,103,177,277,303,71,70,84,85,138,142,67,71,187,70,57,74,80,117,118,104,86,77,88,68,161", "endOffsets": "126,208,328,388,455,547,607,675,753,857,1035,1313,1617,1689,1760,1845,1931,2070,2213,2281,2353,2541,2612,2670,2745,2826,2944,3063,3168,3255,3333,3422,3491,3653"}, "to": {"startLines": "694,696,841,859,864,926,939,940,941,942,943,944,945,960,961,962,963,964,965,966,967,970,971,972,973,974,975,976,977,978,979,980,981,982", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "65561,65728,79403,81238,81596,88064,89976,90036,90104,90182,90286,90464,90742,92160,92232,92303,92388,92474,92613,92756,92824,93045,93233,93304,93362,93437,93518,93636,93755,93860,93947,94025,94114,94183", "endColumns": "75,81,119,59,66,91,59,67,77,103,177,277,303,71,70,84,85,138,142,67,71,187,70,57,74,80,117,118,104,86,77,88,68,161", "endOffsets": "65632,65805,79518,81293,81658,88151,90031,90099,90177,90281,90459,90737,91041,92227,92298,92383,92469,92608,92751,92819,92891,93228,93299,93357,93432,93513,93631,93750,93855,93942,94020,94109,94178,94340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "217", "startColumns": "4", "startOffsets": "22069", "endColumns": "131", "endOffsets": "22196"}}]}]}