{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7c68162bc9aa811289901f5e22f4653a\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,13818", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,13896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a5ece3c7dc0bc71837804011c458ad9\\transformed\\jetified-hcaptcha-20.52.3\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "76", "endOffsets": "127"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "14074", "endColumns": "76", "endOffsets": "14146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3b21ac5fe30d157215f8c70dc88df410\\transformed\\material-1.11.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1051,1136,1204,1268,1355,1419,1483,1542,1614,1678,1732,1851,1911,1972,2026,2099,2232,2316,2409,2547,2627,2706,2832,2920,2999,3054,3105,3171,3244,3323,3409,3488,3561,3636,3710,3782,3895,3983,4060,4151,4243,4315,4389,4480,4534,4616,4685,4768,4854,4916,4980,5043,5111,5214,5317,5414,5515,5574,5629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "261,341,420,505,597,684,783,900,982,1046,1131,1199,1263,1350,1414,1478,1537,1609,1673,1727,1846,1906,1967,2021,2094,2227,2311,2404,2542,2622,2701,2827,2915,2994,3049,3100,3166,3239,3318,3404,3483,3556,3631,3705,3777,3890,3978,4055,4146,4238,4310,4384,4475,4529,4611,4680,4763,4849,4911,4975,5038,5106,5209,5312,5409,5510,5569,5624,5705"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3264,3344,3423,3508,3600,4416,4515,4632,7704,7933,8385,8622,8686,8773,8837,8901,8960,9032,9096,9150,9269,9329,9390,9444,9517,9650,9734,9827,9965,10045,10124,10250,10338,10417,10472,10523,10589,10662,10741,10827,10906,10979,11054,11128,11200,11313,11401,11478,11569,11661,11733,11807,11898,11952,12034,12103,12186,12272,12334,12398,12461,12529,12632,12735,12832,12933,12992,13432", "endLines": "5,35,36,37,38,39,47,48,49,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,149", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,125,87,78,54,50,65,72,78,85,78,72,74,73,71,112,87,76,90,91,71,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80", "endOffsets": "311,3339,3418,3503,3595,3682,4510,4627,4709,7763,8013,8448,8681,8768,8832,8896,8955,9027,9091,9145,9264,9324,9385,9439,9512,9645,9729,9822,9960,10040,10119,10245,10333,10412,10467,10518,10584,10657,10736,10822,10901,10974,11049,11123,11195,11308,11396,11473,11564,11656,11728,11802,11893,11947,12029,12098,12181,12267,12329,12393,12456,12524,12627,12730,12827,12928,12987,13042,13508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f63fc8e219d0da2c2381781a446b35d\\transformed\\preference-1.2.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,267,343,481,650,733", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "175,262,338,476,645,728,806"}, "to": {"startLines": "71,78,146,150,157,161,162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7183,7768,13222,13513,14151,14590,14673", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "7253,7850,13293,13646,14315,14668,14746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f9f62d475da5647ccd554e09d9a5175\\transformed\\jetified-ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "50,51,73,74,76,86,87,144,145,147,148,151,152,154,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4714,4807,7358,7454,7622,8453,8531,13047,13138,13298,13366,13651,13733,13901,14320,14397,14468", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "4802,4882,7449,7544,7699,8526,8617,13133,13217,13361,13427,13728,13813,13968,14392,14463,14585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a7641d5886647657d0030d119d80c30f\\transformed\\jetified-play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4965,5068,5230,5357,5465,5615,5744,5860,6103,6263,6371,6535,6667,6822,6967,7030,7095", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "5063,5225,5352,5460,5610,5739,5855,5958,6258,6366,6530,6662,6817,6962,7025,7090,7178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a44aaec257ce2cc8cac45c8fbfc80c0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,115", "endOffsets": "160,276"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3038,3148", "endColumns": "109,115", "endOffsets": "3143,3259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\831c37758e4c632d9b51d3452f8c90e3\\transformed\\jetified-play-services-wallet-19.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "84,163", "startColumns": "4,4", "startOffsets": "8324,14751", "endColumns": "60,75", "endOffsets": "8380,14822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\483091e0b4c7d0c83ff9b4e39e422f52\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5963", "endColumns": "139", "endOffsets": "6098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59d0c815c7d39ad0cf5643a81ed8f019\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "40,41,42,43,44,45,46,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3687,3782,3884,3982,4085,4191,4296,13973", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3777,3879,3977,4080,4186,4291,4411,14069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6580ba3899d752f0adcab81246e19f4\\transformed\\jetified-material3-1.0.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,206", "endColumns": "77,72,77", "endOffsets": "128,201,279"}, "to": {"startLines": "52,75,79", "startColumns": "4,4,4", "startOffsets": "4887,7549,7855", "endColumns": "77,72,77", "endOffsets": "4960,7617,7928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a6b9eb336c3d2a2816400f2435a9ad9\\transformed\\browser-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "72,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7258,8018,8118,8225", "endColumns": "99,99,106,98", "endOffsets": "7353,8113,8220,8319"}}]}]}