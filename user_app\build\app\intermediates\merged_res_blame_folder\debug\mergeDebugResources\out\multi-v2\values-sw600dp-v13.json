{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b575c095cc78a72a353244b717a5c9bd\\transformed\\appcompat-1.6.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}, "to": {"startLines": "3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "93,162,231,301,375,451,510,581", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "157,226,296,370,446,505,576,644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8310984e25160b155f68ac9a70a9513\\transformed\\material-1.11.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,673,732,795", "endLines": "2,3,4,5,6,7,9,10,11,12,13,17", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "110,180,251,323,381,439,548,612,668,727,790,962"}, "to": {"startLines": "11,12,13,14,15,16,17,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "649,709,779,850,922,980,1038,1147,1211,1267,1326,1389", "endLines": "11,12,13,14,15,16,18,19,20,21,22,26", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "704,774,845,917,975,1033,1142,1206,1262,1321,1384,1556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1445e7ae71fec047506ab9dd37581292\\transformed\\jetified-uikit-1.31.1-SANDBOX\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "37", "endOffsets": "88"}}]}]}