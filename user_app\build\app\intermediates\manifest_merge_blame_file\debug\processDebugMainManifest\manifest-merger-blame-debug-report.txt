1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.nafiss.user"
4    android:versionCode="96"
5    android:versionName="11.11.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:5-67
14-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:5:5-79
15-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:5:22-76
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:6:5-65
16-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:6:22-62
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:7:5-79
17-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:7:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:8:5-81
18-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:8:22-78
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:9:5-77
19-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:9:22-74
20    <uses-permission android:name="android.permission.RECORD_AUDIO" />
20-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:10:5-70
20-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:10:22-68
21    <uses-permission android:name="android.permission.BLUETOOTH" />
21-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:11:5-67
21-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:11:22-65
22    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
22-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:12:5-73
22-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:12:22-71
23    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
23-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:13:5-75
23-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:13:22-73
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
24-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
25    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
25-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
25-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
26-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
26-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" /> <!-- Required by older versions of Google Play services to create IID tokens -->
27-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
27-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
28    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
28-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
28-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
29
30    <queries>
30-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
31        <intent>
31-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
32            <action android:name="android.support.customtabs.action.CustomTabsService" />
32-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
32-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
33        </intent>
34
35        <package android:name="com.gojek.app" />
35-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:17:9-49
35-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:17:18-46
36        <package android:name="com.phonepe.app" />
36-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:8:9-51
36-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:8:18-48
37        <package android:name="com.phonepe.app.preprod" />
37-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:9:9-59
37-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:9:18-56
38        <package android:name="com.phonepe.simulator" />
38-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:10:9-57
38-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:10:18-54
39        <package android:name="com.phonepe.simulator.debug" />
39-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:11:9-63
39-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:11:18-60
40
41        <intent>
41-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:13:9-19:18
42            <action android:name="android.intent.action.VIEW" />
42-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
42-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
43
44            <data
44-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
45                android:host="pay"
45-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
46                android:scheme="upi" />
46-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
47        </intent>
48        <intent>
48-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:20:9-26:18
49            <action android:name="android.intent.action.VIEW" />
49-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
49-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
50
51            <data
51-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
52                android:host="upi"
52-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
53                android:scheme="mandate" />
53-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
54        </intent> <!-- Added to check the default browser that will host the AuthFlow. -->
55        <intent>
55-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:13:9-17:18
56            <action android:name="android.intent.action.VIEW" />
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
56-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
57
58            <data android:scheme="http" />
58-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
58-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
59        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
60        <package android:name="com.android.chrome" />
60-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:9-54
60-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:18-51
61
62        <intent>
62-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:18
63            <action android:name="android.intent.action.GET_CONTENT" />
63-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-72
63-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:21-69
64
65            <data android:mimeType="*/*" />
65-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
66        </intent> <!-- Needs to be explicitly declared on Android R+ -->
67        <package android:name="com.google.android.apps.maps" />
67-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
67-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
68    </queries>
69
70    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
70-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:14:5-75
70-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:14:22-72
71
72    <meta-data
72-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:32:5-34:32
73        android:name="android.webkit.WebView.EnableSafeBrowsing"
73-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:33:9-65
74        android:value="true" />
74-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:34:9-29
75
76    <uses-permission
76-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
77        android:name="android.permission.READ_EXTERNAL_STORAGE"
77-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-64
78        android:maxSdkVersion="32" />
78-->[:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
79    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Don't require camera, as this requires a rear camera. This allows it to work on the Nexus 7 -->
79-->[:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
79-->[:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
80    <uses-feature
80-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:24:5-26:36
81        android:name="android.hardware.camera"
81-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:25:9-47
82        android:required="false" />
82-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:26:9-33
83    <uses-feature
83-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:27:5-29:36
84        android:name="android.hardware.camera.front"
84-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:28:9-53
85        android:required="false" /> <!-- TODO replace above two with next line after Android 4.2 -->
85-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:29:9-33
86    <!-- <uses-feature android:name="android.hardware.camera.any"/> -->
87    <uses-feature
87-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:32:5-34:36
88        android:name="android.hardware.camera.autofocus"
88-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:33:9-57
89        android:required="false" />
89-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:34:9-33
90    <uses-feature
90-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:35:5-37:36
91        android:name="android.hardware.camera.flash"
91-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:36:9-53
92        android:required="false" />
92-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:37:9-33
93    <uses-feature
93-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:38:5-40:36
94        android:name="android.hardware.screen.landscape"
94-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:39:9-57
95        android:required="false" />
95-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:40:9-33
96    <uses-feature
96-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:41:5-43:36
97        android:name="android.hardware.wifi"
97-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:42:9-45
98        android:required="false" />
98-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:43:9-33
99    <uses-feature
99-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
100        android:glEsVersion="0x00020000"
100-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
101        android:required="true" />
101-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
102
103    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
103-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
103-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
104    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
104-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:5-98
104-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:22-95
105
106    <permission
106-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
107        android:name="com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
107-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
108        android:protectionLevel="signature" />
108-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
109
110    <uses-permission android:name="com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
110-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
111
112    <application
112-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:15:5-96:19
113        android:allowBackup="false"
113-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:21:9-36
114        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
114-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
115        android:debuggable="true"
116        android:extractNativeLibs="false"
117        android:icon="@mipmap/ic_launcher"
117-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:16:9-43
118        android:label="TENDEEL User"
118-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:17:9-37
119        android:requestLegacyExternalStorage="true"
119-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:18:9-52
120        android:supportsRtl="true"
120-->[com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:16:9-35
121        android:usesCleartextTraffic="false" >
121-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:22:9-45
122        <meta-data
122-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:20:9-22:72
123            android:name="com.google.firebase.messaging.default_notification_icon"
123-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:21:17-87
124            android:resource="@drawable/ic_stat_ic_notification" />
124-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:22:17-69
125
126        <activity
126-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:24:9-65:20
127            android:name="com.nafiss.user.MainActivity"
127-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:25:13-41
128            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
128-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:26:13-163
129            android:exported="true"
129-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:29:13-36
130            android:hardwareAccelerated="true"
130-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:27:13-47
131            android:launchMode="singleTop"
131-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:28:13-43
132            android:theme="@style/LaunchTheme"
132-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:30:13-47
133            android:windowSoftInputMode="adjustResize" >
133-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:31:13-55
134
135            <!--
136                 Specifies an Android theme to apply to this Activity as soon as
137                 the Android process has started. This theme is visible to the user
138                 while the Flutter UI initializes. After that, this theme continues
139                 to determine the Window background behind the Flutter UI.
140            -->
141            <meta-data
141-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:36:13-38:57
142                android:name="io.flutter.embedding.android.NormalTheme"
142-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:37:17-72
143                android:resource="@style/NormalTheme" />
143-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:38:17-54
144
145            <!--
146                 Displays an Android View that continues showing the launch screen
147                 Drawable until Flutter paints its first frame, then this splash
148                 screen fades out. A splash screen is useful to avoid any visual
149                 gap between the end of Android's launch screen and the painting of
150                 Flutter's first frame.
151            -->
152            <meta-data
152-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:45:13-47:66
153                android:name="io.flutter.embedding.android.SplashScreenDrawable"
153-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:46:17-81
154                android:resource="@drawable/launch_background" />
154-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:47:17-63
155
156            <intent-filter>
156-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:49:13-52:29
157                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
157-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:50:17-69
157-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:50:25-66
158
159                <category android:name="android.intent.category.DEFAULT" />
159-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
159-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
160            </intent-filter>
161            <intent-filter>
161-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:54:13-58:29
162                <action android:name="android.intent.action.MAIN" />
162-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:55:17-69
162-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:55:25-66
163
164                <category android:name="android.intent.category.LAUNCHER" />
164-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:56:17-77
164-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:56:27-74
165            </intent-filter>
166
167            <meta-data
167-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:20:9-22:72
168                android:name="com.google.firebase.messaging.default_notification_icon"
168-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:21:17-87
169                android:resource="@drawable/ic_stat_ic_notification" />
169-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:22:17-69
170            <meta-data
170-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:62:13-64:47
171                android:name="com.google.firebase.messaging.default_notification_channel_id"
171-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:63:17-93
172                android:value="notification" />
172-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:64:17-45
173        </activity>
174        <!--
175        <activity
176            android:name="com.braintreepayments.api.BraintreeBrowserSwitchActivity"
177            android:exported="false"
178            android:launchMode="singleTask"
179            tools:ignore="AppLinkUrlError">
180            <intent-filter>
181                <action android:name="android.intent.action.VIEW" />
182
183                <category android:name="android.intent.category.DEFAULT" />
184                <category android:name="android.intent.category.BROWSABLE" />
185
186                <data android:scheme="${applicationId}.braintree" />
187            </intent-filter>
188        </activity>
189        -->
190
191        <meta-data
191-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:81:9-83:36
192            android:name="com.google.android.gms.wallet.api.enabled"
192-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:82:13-69
193            android:value="true" />
193-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:83:13-33
194        <meta-data
194-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:85:9-87:51
195            android:name="com.google.android.geo.API_KEY"
195-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:86:13-58
196            android:value="Your_Google_Map_key" />
196-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:87:13-48
197        <!--
198             Don't delete the meta-data below.
199             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
200        -->
201        <meta-data
201-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:90:9-92:33
202            android:name="flutterEmbedding"
202-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:91:13-44
203            android:value="2" />
203-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:92:13-30
204
205        <service
205-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:93:9-95:56
206            android:name="com.nafiss.user.MyNavigationService"
206-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:94:13-47
207            android:foregroundServiceType="location" />
207-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:95:13-53
208
209        <activity
209-->[:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:54
210            android:name="co.paystack.flutterpaystack.AuthActivity"
210-->[:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
211            android:theme="@style/Paystack.Dialog" />
211-->[:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-51
212        <!--
213           Declares a provider which allows us to store files to share in
214           '.../caches/share_plus' and grant the receiving action access
215        -->
216        <provider
216-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
217            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
217-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
218            android:authorities="com.nafiss.user.flutter.share_provider"
218-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
219            android:exported="false"
219-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
220            android:grantUriPermissions="true" >
220-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
221            <meta-data
221-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
223                android:resource="@xml/flutter_share_file_paths" />
223-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
224        </provider>
225        <!--
226           This manifest declared broadcast receiver allows us to use an explicit
227           Intent when creating a PendingItent to be informed of the user's choice
228        -->
229        <receiver
229-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
230            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
230-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
231            android:exported="false" >
231-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
232            <intent-filter>
232-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
233                <action android:name="EXTRA_CHOSEN_COMPONENT" />
233-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
233-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
234            </intent-filter>
235        </receiver>
236
237        <service
237-->[:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
238            android:name="com.google.firebase.components.ComponentDiscoveryService"
238-->[:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
239            android:directBootAware="true"
239-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
240            android:exported="false" >
240-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:16:13-37
241            <meta-data
241-->[:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
242                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
242-->[:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
244            <meta-data
244-->[:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
245                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
245-->[:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
247            <meta-data
247-->[:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
248                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
248-->[:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
250            <meta-data
250-->[:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
251                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
251-->[:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
253            <meta-data
253-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
254                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
254-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
256            <meta-data
256-->[:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
257                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
257-->[:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
259            <meta-data
259-->[:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
260                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
260-->[:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
262            <meta-data
262-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
263                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
263-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
265            <meta-data
265-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
266                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
266-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
268            <meta-data
268-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
269                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
269-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
271            <meta-data
271-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:15:13-17:85
272                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
272-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:16:17-126
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:17:17-82
274            <meta-data
274-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:18:13-20:85
275                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
275-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:19:17-115
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:20:17-82
277            <meta-data
277-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
278                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
278-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
279                android:value="com.google.firebase.components.ComponentRegistrar" />
279-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
280            <meta-data
280-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:29:13-31:85
281                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
281-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:30:17-117
282                android:value="com.google.firebase.components.ComponentRegistrar" />
282-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:31:17-82
283            <meta-data
283-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
284                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
284-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
285                android:value="com.google.firebase.components.ComponentRegistrar" />
285-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
286            <meta-data
286-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
287                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
287-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
288                android:value="com.google.firebase.components.ComponentRegistrar" />
288-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
289            <meta-data
289-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
290                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
290-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
291                android:value="com.google.firebase.components.ComponentRegistrar" />
291-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
292            <meta-data
292-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
293                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
293-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
294                android:value="com.google.firebase.components.ComponentRegistrar" />
294-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
295            <meta-data
295-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
296                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
296-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
297                android:value="com.google.firebase.components.ComponentRegistrar" />
297-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
298            <meta-data
298-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
299                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
299-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
300                android:value="com.google.firebase.components.ComponentRegistrar" />
300-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
301            <meta-data
301-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
302                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
302-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
303                android:value="com.google.firebase.components.ComponentRegistrar" />
303-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
304            <meta-data
304-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
305                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
305-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
306                android:value="com.google.firebase.components.ComponentRegistrar" />
306-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
307            <meta-data
307-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
308                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
308-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
309                android:value="com.google.firebase.components.ComponentRegistrar" />
309-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
310            <meta-data
310-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
311                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
311-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
312                android:value="com.google.firebase.components.ComponentRegistrar" />
312-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
313            <meta-data
313-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
314                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
314-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
315                android:value="com.google.firebase.components.ComponentRegistrar" />
315-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
316            <meta-data
316-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
317                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
317-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
318                android:value="com.google.firebase.components.ComponentRegistrar" />
318-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
319            <meta-data
319-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
320                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
320-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
321                android:value="com.google.firebase.components.ComponentRegistrar" />
321-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
322        </service>
323        <service
323-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
324            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
324-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
325            android:exported="false"
325-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
326            android:permission="android.permission.BIND_JOB_SERVICE" />
326-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
327        <service
327-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
328            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
328-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
329            android:exported="false" >
329-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
330            <intent-filter>
330-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
331                <action android:name="com.google.firebase.MESSAGING_EVENT" />
331-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
331-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
332            </intent-filter>
333        </service>
334
335        <receiver
335-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
336            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
336-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
337            android:exported="true"
337-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
338            android:permission="com.google.android.c2dm.permission.SEND" >
338-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
339            <intent-filter>
339-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
340                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
340-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
340-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
341            </intent-filter>
342        </receiver>
343
344        <provider
344-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
345            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
345-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
346            android:authorities="com.nafiss.user.flutterfirebasemessaginginitprovider"
346-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
347            android:exported="false"
347-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
348            android:initOrder="99" />
348-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
349
350        <activity
350-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
351            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
351-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
352            android:excludeFromRecents="true"
352-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
353            android:exported="true"
353-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
354            android:launchMode="singleTask"
354-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
355            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
355-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
356            <intent-filter>
356-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
357                <action android:name="android.intent.action.VIEW" />
357-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
357-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
358
359                <category android:name="android.intent.category.DEFAULT" />
359-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
359-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
360                <category android:name="android.intent.category.BROWSABLE" />
360-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
360-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
361
362                <data
362-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
363                    android:host="firebase.auth"
363-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
364                    android:path="/"
364-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
365                    android:scheme="genericidp" />
365-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
366            </intent-filter>
367        </activity>
368        <activity
368-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
369            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
369-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
370            android:excludeFromRecents="true"
370-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
371            android:exported="true"
371-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
372            android:launchMode="singleTask"
372-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
373            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
373-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
374            <intent-filter>
374-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
375                <action android:name="android.intent.action.VIEW" />
375-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
375-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
376
377                <category android:name="android.intent.category.DEFAULT" />
377-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
377-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
378                <category android:name="android.intent.category.BROWSABLE" />
378-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
378-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
379
380                <data
380-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
381                    android:host="firebase.auth"
381-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
382                    android:path="/"
382-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
383                    android:scheme="recaptcha" />
383-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
384            </intent-filter>
385        </activity>
386
387        <property
387-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
388            android:name="android.adservices.AD_SERVICES_CONFIG"
388-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
389            android:resource="@xml/ga_ad_services_config" />
389-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
390
391        <service
391-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:22:9-25:40
392            android:name="com.google.firebase.sessions.SessionLifecycleService"
392-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:23:13-80
393            android:enabled="true"
393-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:24:13-35
394            android:exported="false" />
394-->[com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:25:13-37
395
396        <receiver
396-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
397            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
397-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
398            android:exported="true"
398-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
399            android:permission="com.google.android.c2dm.permission.SEND" >
399-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
400            <intent-filter>
400-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
401                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
401-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
401-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
402            </intent-filter>
403
404            <meta-data
404-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
405                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
405-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
406                android:value="true" />
406-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
407        </receiver>
408        <!--
409             FirebaseMessagingService performs security checks at runtime,
410             but set to not exported to explicitly avoid allowing another app to call it.
411        -->
412        <service
412-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
413            android:name="com.google.firebase.messaging.FirebaseMessagingService"
413-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
414            android:directBootAware="true"
414-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
415            android:exported="false" >
415-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
416            <intent-filter android:priority="-500" >
416-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
417                <action android:name="com.google.firebase.MESSAGING_EVENT" />
417-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
417-->[:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
418            </intent-filter>
419        </service>
420
421        <provider
421-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
422            android:name="com.google.firebase.provider.FirebaseInitProvider"
422-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
423            android:authorities="com.nafiss.user.firebaseinitprovider"
423-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
424            android:directBootAware="true"
424-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
425            android:exported="false"
425-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
426            android:initOrder="100" />
426-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
427
428        <activity
428-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
429            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
429-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
430            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
430-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
431            android:exported="false"
431-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
432            android:theme="@style/AppTheme" />
432-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
433        <activity
433-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
434            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
434-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
435            android:exported="false"
435-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
436            android:theme="@style/ThemeTransparent" />
436-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
437        <activity
437-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
438            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
438-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
439            android:exported="false"
439-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
440            android:theme="@style/ThemeTransparent" />
440-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
441        <activity
441-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
442            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
442-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
443            android:exported="false"
443-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
444            android:launchMode="singleInstance"
444-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
445            android:theme="@style/ThemeTransparent" />
445-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
446        <activity
446-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
447            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
447-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
448            android:exported="false"
448-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
449            android:launchMode="singleInstance"
449-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
450            android:theme="@style/ThemeTransparent" />
450-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
451
452        <receiver
452-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
453            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
453-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
454            android:enabled="true"
454-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
455            android:exported="false" />
455-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
456
457        <meta-data
457-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
458            android:name="io.flutter.embedded_views_preview"
458-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
459            android:value="true" />
459-->[:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
460
461        <activity
461-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:24:9-27:67
462            android:name="com.midtrans.sdk.uikit.activities.UserDetailsActivity"
462-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:25:13-81
463            android:screenOrientation="portrait"
463-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:26:13-49
464            android:windowSoftInputMode="stateHidden|adjustPan" />
464-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:27:13-64
465        <activity
465-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:28:9-31:52
466            android:name="com.midtrans.sdk.uikit.activities.PaymentMethodsActivity"
466-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:29:13-84
467            android:configChanges="keyboardHidden|orientation|screenSize"
467-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:30:13-74
468            android:screenOrientation="portrait" />
468-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:31:13-49
469        <activity
469-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:32:9-36:67
470            android:name="com.midtrans.sdk.uikit.views.mandiri_clickpay.MandiriClickPayActivity"
470-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:33:13-97
471            android:configChanges="keyboardHidden|orientation|screenSize"
471-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:34:13-74
472            android:screenOrientation="portrait"
472-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:35:13-49
473            android:windowSoftInputMode="stateHidden|adjustPan" />
473-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:36:13-64
474        <activity
474-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:37:9-40:52
475            android:name="com.midtrans.sdk.uikit.views.bri_epay.BriEpayPaymentActivity"
475-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:38:13-88
476            android:configChanges="keyboardHidden|orientation|screenSize"
476-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:39:13-74
477            android:screenOrientation="portrait" />
477-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:40:13-49
478        <activity
478-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:41:9-44:52
479            android:name="com.midtrans.sdk.uikit.views.cimb_click.CimbClickPaymentActivity"
479-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:42:13-92
480            android:configChanges="keyboardHidden|orientation|screenSize"
480-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:43:13-74
481            android:screenOrientation="portrait" />
481-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:44:13-49
482        <activity
482-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:45:9-48:52
483            android:name="com.midtrans.sdk.uikit.views.mandiri_ecash.MandiriEcashPaymentActivity"
483-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:46:13-98
484            android:configChanges="keyboardHidden|orientation|screenSize"
484-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:47:13-74
485            android:screenOrientation="portrait" />
485-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:48:13-49
486        <activity
486-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:49:9-53:67
487            android:name="com.midtrans.sdk.uikit.views.indosat_dompetku.IndosatDompetkuPaymentActivity"
487-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:50:13-104
488            android:configChanges="keyboardHidden|orientation|screenSize"
488-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:51:13-74
489            android:screenOrientation="portrait"
489-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:52:13-49
490            android:windowSoftInputMode="stateHidden|adjustPan" />
490-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:53:13-64
491        <activity
491-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:54:9-57:52
492            android:name="com.midtrans.sdk.uikit.views.indomaret.payment.IndomaretPaymentActivity"
492-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:55:13-99
493            android:configChanges="keyboardHidden|orientation|screenSize"
493-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:56:13-74
494            android:screenOrientation="portrait" />
494-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:57:13-49
495        <activity
495-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:58:9-61:52
496            android:name="com.midtrans.sdk.uikit.views.indomaret.status.IndomaretStatusActivity"
496-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:59:13-97
497            android:configChanges="keyboardHidden|orientation|screenSize"
497-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:60:13-74
498            android:screenOrientation="portrait" />
498-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:61:13-49
499        <activity
499-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:62:9-66:67
500            android:name="com.midtrans.sdk.uikit.views.telkomsel_cash.TelkomselCashPaymentActivity"
500-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:63:13-100
501            android:configChanges="keyboardHidden|orientation|screenSize"
501-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:64:13-74
502            android:screenOrientation="portrait"
502-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:65:13-49
503            android:windowSoftInputMode="stateHidden|adjustPan" />
503-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:66:13-64
504        <activity
504-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:67:9-70:52
505            android:name="com.midtrans.sdk.uikit.views.xl_tunai.payment.XlTunaiPaymentActivity"
505-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:68:13-96
506            android:configChanges="keyboardHidden|orientation|screenSize"
506-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:69:13-74
507            android:screenOrientation="portrait" />
507-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:70:13-49
508        <activity
508-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:71:9-74:52
509            android:name="com.midtrans.sdk.uikit.views.xl_tunai.status.XlTunaiStatusActivity"
509-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:72:13-94
510            android:configChanges="keyboardHidden|orientation|screenSize"
510-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:73:13-74
511            android:screenOrientation="portrait" />
511-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:74:13-49
512        <activity
512-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:75:9-78:52
513            android:name="com.midtrans.sdk.uikit.views.xl_tunai.XlTunaiInstructionActivity"
513-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:76:13-92
514            android:configChanges="keyboardHidden|orientation|screenSize"
514-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:77:13-74
515            android:screenOrientation="portrait" />
515-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:78:13-49
516        <activity
516-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:79:9-82:52
517            android:name="com.midtrans.sdk.uikit.views.kioson.payment.KiosonPaymentActivity"
517-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:80:13-93
518            android:configChanges="keyboardHidden|orientation|screenSize"
518-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:81:13-74
519            android:screenOrientation="portrait" />
519-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:82:13-49
520        <activity
520-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:83:9-86:52
521            android:name="com.midtrans.sdk.uikit.views.kioson.status.KiosonStatusActivity"
521-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:84:13-91
522            android:configChanges="keyboardHidden|orientation|screenSize"
522-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:85:13-74
523            android:screenOrientation="portrait" />
523-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:86:13-49
524        <activity
524-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:87:9-91:67
525            android:name="com.midtrans.sdk.uikit.views.gci.GciPaymentActivity"
525-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:88:13-79
526            android:configChanges="keyboardHidden|orientation|screenSize"
526-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:89:13-74
527            android:screenOrientation="portrait"
527-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:90:13-49
528            android:windowSoftInputMode="stateHidden|adjustPan" />
528-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:91:13-64
529        <activity
529-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:92:9-96:67
530            android:name="com.midtrans.sdk.uikit.views.creditcard.details.CreditCardDetailsActivity"
530-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:93:13-101
531            android:configChanges="keyboardHidden|orientation|screenSize"
531-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:94:13-74
532            android:screenOrientation="portrait"
532-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:95:13-49
533            android:windowSoftInputMode="stateHidden|adjustPan" />
533-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:96:13-64
534        <activity
534-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:97:9-100:52
535            android:name="com.midtrans.sdk.uikit.views.status.PaymentStatusActivity"
535-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:98:13-85
536            android:configChanges="keyboardHidden|orientation|screenSize"
536-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:99:13-74
537            android:screenOrientation="portrait" />
537-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:100:13-49
538        <activity
538-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:101:9-104:52
539            android:name="com.midtrans.sdk.uikit.views.creditcard.saved.SavedCreditCardActivity"
539-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:102:13-97
540            android:configChanges="keyboardHidden|orientation|screenSize"
540-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:103:13-74
541            android:screenOrientation="portrait" />
541-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:104:13-49
542        <activity
542-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:105:9-109:76
543            android:name="com.midtrans.sdk.uikit.views.creditcard.bankpoints.BankPointsActivity"
543-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:106:13-97
544            android:configChanges="keyboardHidden|orientation|screenSize"
544-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:107:13-74
545            android:screenOrientation="portrait"
545-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:108:13-49
546            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
546-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:109:13-73
547        <activity
547-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:110:9-113:52
548            android:name="com.midtrans.sdk.uikit.views.banktransfer.list.BankTransferListActivity"
548-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:111:13-99
549            android:configChanges="keyboardHidden|orientation|screenSize"
549-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:112:13-74
550            android:screenOrientation="portrait" />
550-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:113:13-49
551        <activity
551-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:114:9-118:67
552            android:name="com.midtrans.sdk.uikit.views.banktransfer.payment.BankTransferPaymentActivity"
552-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:115:13-105
553            android:configChanges="keyboardHidden|orientation|screenSize"
553-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:116:13-74
554            android:screenOrientation="portrait"
554-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:117:13-49
555            android:windowSoftInputMode="stateHidden|adjustPan" />
555-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:118:13-64
556        <activity
556-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:119:9-122:52
557            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.VaPaymentStatusActivity"
557-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:120:13-100
558            android:configChanges="keyboardHidden|orientation|screenSize"
558-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:121:13-74
559            android:screenOrientation="portrait" />
559-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:122:13-49
560        <activity
560-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:123:9-126:52
561            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.VaOtherBankPaymentStatusActivity"
561-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:124:13-109
562            android:configChanges="keyboardHidden|orientation|screenSize"
562-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:125:13-74
563            android:screenOrientation="portrait" />
563-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:126:13-49
564        <activity
564-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:127:9-130:52
565            android:name="com.midtrans.sdk.uikit.views.banktransfer.status.MandiriBillStatusActivity"
565-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:128:13-102
566            android:configChanges="keyboardHidden|orientation|screenSize"
566-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:129:13-74
567            android:screenOrientation="portrait" />
567-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:130:13-49
568        <activity
568-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:131:9-135:67
569            android:name="com.midtrans.sdk.uikit.views.creditcard.register.CardRegistrationActivity"
569-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:132:13-101
570            android:configChanges="keyboardHidden|orientation|screenSize"
570-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:133:13-74
571            android:screenOrientation="portrait"
571-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:134:13-49
572            android:windowSoftInputMode="stateHidden|adjustPan" />
572-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:135:13-64
573        <activity
573-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:136:9-139:52
574            android:name="com.midtrans.sdk.uikit.views.gopay.payment.GoPayPaymentActivity"
574-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:137:13-91
575            android:configChanges="keyboardHidden|orientation|screenSize"
575-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:138:13-74
576            android:screenOrientation="portrait" />
576-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:139:13-49
577        <activity
577-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:140:9-143:52
578            android:name="com.midtrans.sdk.uikit.views.shopeepay.payment.ShopeePayPaymentActivity"
578-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:141:13-99
579            android:configChanges="keyboardHidden|orientation|screenSize"
579-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:142:13-74
580            android:screenOrientation="portrait" />
580-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:143:13-49
581        <activity
581-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:144:9-147:52
582            android:name="com.midtrans.sdk.uikit.views.gopay.status.GoPayStatusActivity"
582-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:145:13-89
583            android:configChanges="keyboardHidden|orientation|screenSize"
583-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:146:13-74
584            android:screenOrientation="portrait" />
584-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:147:13-49
585        <activity
585-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:148:9-151:52
586            android:name="com.midtrans.sdk.uikit.views.shopeepay.status.ShopeePayStatusActivity"
586-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:149:13-97
587            android:configChanges="keyboardHidden|orientation|screenSize"
587-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:150:13-74
588            android:screenOrientation="portrait" />
588-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:151:13-49
589        <activity
589-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:152:9-155:52
590            android:name="com.midtrans.sdk.uikit.views.danamon_online.DanamonOnlineActivity"
590-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:153:13-93
591            android:configChanges="keyboardHidden|orientation|screenSize"
591-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:154:13-74
592            android:screenOrientation="portrait" />
592-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:155:13-49
593        <activity
593-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:156:9-160:67
594            android:name="com.midtrans.sdk.uikit.views.bca_klikbca.payment.KlikBcaPaymentActivity"
594-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:157:13-99
595            android:configChanges="keyboardHidden|orientation|screenSize"
595-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:158:13-74
596            android:screenOrientation="portrait"
596-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:159:13-49
597            android:windowSoftInputMode="stateHidden|adjustPan" />
597-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:160:13-64
598        <activity
598-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:161:9-164:52
599            android:name="com.midtrans.sdk.uikit.views.bca_klikbca.status.KlikBcaStatusActivity"
599-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:162:13-97
600            android:configChanges="keyboardHidden|orientation|screenSize"
600-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:163:13-74
601            android:screenOrientation="portrait" />
601-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:164:13-49
602        <activity
602-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:165:9-168:52
603            android:name="com.midtrans.sdk.uikit.views.bca_klikpay.BcaKlikPayPaymentActivity"
603-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:166:13-94
604            android:configChanges="keyboardHidden|orientation|screenSize"
604-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:167:13-74
605            android:screenOrientation="portrait" />
605-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:168:13-49
606        <activity
606-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:169:9-172:52
607            android:name="com.midtrans.sdk.uikit.views.alfamart.payment.AlfamartPaymentActivity"
607-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:170:13-97
608            android:configChanges="keyboardHidden|orientation|screenSize"
608-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:171:13-74
609            android:screenOrientation="portrait" />
609-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:172:13-49
610        <activity
610-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:173:9-176:52
611            android:name="com.midtrans.sdk.uikit.views.alfamart.status.AlfamartStatusActivity"
611-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:174:13-95
612            android:configChanges="keyboardHidden|orientation|screenSize"
612-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:175:13-74
613            android:screenOrientation="portrait" />
613-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:176:13-49
614        <activity
614-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:177:9-180:52
615            android:name="com.midtrans.sdk.uikit.views.webview.WebViewPaymentActivity"
615-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:178:13-87
616            android:configChanges="keyboardHidden|orientation|screenSize"
616-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:179:13-74
617            android:screenOrientation="portrait" />
617-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:180:13-49
618        <activity
618-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:181:9-184:52
619            android:name="com.midtrans.sdk.uikit.views.creditcard.tnc.TermsAndConditionsActivity"
619-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:182:13-98
620            android:configChanges="keyboardHidden|orientation|screenSize"
620-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:183:13-74
621            android:screenOrientation="portrait" />
621-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:184:13-49
622        <activity
622-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:185:9-188:52
623            android:name="com.midtrans.sdk.uikit.views.akulaku.AkulakuActivity"
623-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:186:13-80
624            android:configChanges="keyboardHidden|orientation|screenSize"
624-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:187:13-74
625            android:screenOrientation="portrait" />
625-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:188:13-49
626        <activity android:name="com.midtrans.sdk.uikit.views.uob.UobListActivity" />
626-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:189:9-85
626-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:189:19-82
627        <activity android:name="com.midtrans.sdk.uikit.views.uob.app.UobAppPaymentActivity" />
627-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:190:9-95
627-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:190:19-92
628        <activity android:name="com.midtrans.sdk.uikit.views.uob.web.UobWebPaymentActivity" />
628-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:191:9-95
628-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:191:19-92
629
630        <service
630-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:193:9-196:52
631            android:name="com.midtrans.raygun.RaygunPostService"
631-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:194:13-65
632            android:exported="false"
632-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:195:13-37
633            android:process=":raygunpostservice" />
633-->[com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:196:13-49
634
635        <activity
635-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:37:9-40:66
636            android:name="com.phonepe.intent.sdk.ui.B2BPGActivity"
636-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:38:13-67
637            android:exported="false"
637-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:39:13-37
638            android:theme="@style/phonepeThemeInvisibleCompat" />
638-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:40:13-63
639        <activity
639-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:41:9-45:67
640            android:name="com.phonepe.intent.sdk.ui.TransactionActivity"
640-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:42:13-73
641            android:configChanges="orientation"
641-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:43:13-48
642            android:theme="@style/phonepeThemeInvisibleCompat"
642-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:44:13-63
643            android:windowSoftInputMode="stateHidden|adjustPan" />
643-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:45:13-64
644
645        <service
645-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:47:9-49:40
646            android:name="com.phonepe.intent.sdk.ui.PreCacheService"
646-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:48:13-69
647            android:exported="false" />
647-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:49:13-37
648
649        <activity
649-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:51:9-53:66
650            android:name="com.phonepe.intent.sdk.ui.OpenIntentTransactionActivity"
650-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:52:13-83
651            android:theme="@style/phonepeThemeInvisibleCompat" />
651-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:53:13-63
652        <activity
652-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:54:9-56:60
653            android:name="com.phonepe.intent.sdk.ui.UpiAppsSelectionDialogActivity"
653-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:55:13-84
654            android:theme="@style/phonepeThemeInvisible" />
654-->[phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:56:13-57
655        <activity
655-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:21:9-65:20
656            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
656-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:22:13-109
657            android:exported="true"
657-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:23:13-36
658            android:launchMode="singleTask" >
658-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:24:13-44
659            <intent-filter>
659-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
660                <action android:name="android.intent.action.VIEW" />
660-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
660-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
661
662                <category android:name="android.intent.category.DEFAULT" />
662-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
662-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
663                <category android:name="android.intent.category.BROWSABLE" />
663-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
663-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
664
665                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
666                <data
666-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
667                    android:host="link-accounts"
667-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
668                    android:pathPrefix="/com.nafiss.user/authentication_return"
669                    android:scheme="stripe-auth" />
669-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
670
671                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
672                <data
672-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
673                    android:host="link-native-accounts"
673-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
674                    android:pathPrefix="/com.nafiss.user/authentication_return"
675                    android:scheme="stripe-auth" />
675-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
676
677                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
678                <data
678-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
679                    android:host="link-accounts"
679-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
680                    android:path="/com.nafiss.user/success"
680-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
681                    android:scheme="stripe-auth" />
681-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
682                <data
682-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
683                    android:host="link-accounts"
683-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
684                    android:path="/com.nafiss.user/cancel"
684-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
685                    android:scheme="stripe-auth" />
685-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
686
687                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
688                <data
688-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
689                    android:host="native-redirect"
689-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
690                    android:pathPrefix="/com.nafiss.user"
691                    android:scheme="stripe-auth" />
691-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
692
693                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
694                <data
694-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
695                    android:host="auth-redirect"
695-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
696                    android:pathPrefix="/com.nafiss.user"
697                    android:scheme="stripe" />
697-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
698            </intent-filter>
699        </activity>
700        <activity
700-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:66:9-69:77
701            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
701-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:67:13-101
702            android:exported="false"
702-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:68:13-37
703            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
703-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:69:13-74
704        <activity
704-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:70:9-74:58
705            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
705-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:71:13-110
706            android:exported="false"
706-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:72:13-37
707            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
707-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:73:13-74
708            android:windowSoftInputMode="adjustResize" />
708-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:74:13-55
709        <activity
709-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:8:9-11:69
710            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
710-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:9:13-80
711            android:exported="false"
711-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:10:13-37
712            android:theme="@style/StripePaymentSheetDefaultTheme" />
712-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:11:13-66
713        <activity
713-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:12:9-15:69
714            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
714-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:13:13-82
715            android:exported="false"
715-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:14:13-37
716            android:theme="@style/StripePaymentSheetDefaultTheme" />
716-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:15:13-66
717        <activity
717-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:16:9-19:69
718            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
718-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:17:13-82
719            android:exported="false"
719-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:18:13-37
720            android:theme="@style/StripePaymentSheetDefaultTheme" />
720-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:19:13-66
721        <activity
721-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:20:9-23:69
722            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
722-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:21:13-97
723            android:exported="false"
723-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:22:13-37
724            android:theme="@style/StripePaymentSheetDefaultTheme" />
724-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:23:13-66
725        <activity
725-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:24:9-27:69
726            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
726-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:25:13-118
727            android:exported="false"
727-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:26:13-37
728            android:theme="@style/StripePaymentSheetDefaultTheme" />
728-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:27:13-66
729        <activity
729-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:28:9-31:69
730            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
730-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:29:13-105
731            android:exported="false"
731-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:30:13-37
732            android:theme="@style/StripePaymentSheetDefaultTheme" />
732-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:31:13-66
733        <activity
733-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:32:9-35:69
734            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
734-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:33:13-82
735            android:exported="false"
735-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:34:13-37
736            android:theme="@style/StripePaymentSheetDefaultTheme" />
736-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:35:13-66
737        <activity
737-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:36:9-39:68
738            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
738-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:37:13-94
739            android:exported="false"
739-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:38:13-37
740            android:theme="@style/StripePayLauncherDefaultTheme" />
740-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:39:13-65
741        <activity
741-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:40:9-42:69
742            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
742-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:41:13-121
743            android:theme="@style/StripePaymentSheetDefaultTheme" />
743-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:42:13-66
744        <activity
744-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:8:9-13:61
745            android:name="com.stripe.android.link.LinkActivity"
745-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:9:13-64
746            android:autoRemoveFromRecents="true"
746-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:10:13-49
747            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
747-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:11:13-115
748            android:launchMode="singleTop"
748-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:12:13-43
749            android:theme="@style/StripeTransparentTheme" />
749-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:13:13-58
750        <activity
750-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:14:9-19:61
751            android:name="com.stripe.android.link.LinkForegroundActivity"
751-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:15:13-74
752            android:autoRemoveFromRecents="true"
752-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:16:13-49
753            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
753-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:17:13-115
754            android:launchMode="singleTop"
754-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:18:13-43
755            android:theme="@style/StripeTransparentTheme" />
755-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:19:13-58
756        <activity
756-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:20:9-37:20
757            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
757-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:21:13-79
758            android:autoRemoveFromRecents="true"
758-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:22:13-49
759            android:exported="true"
759-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:23:13-36
760            android:launchMode="singleInstance"
760-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:24:13-48
761            android:theme="@style/StripeTransparentTheme" >
761-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:25:13-58
762            <intent-filter>
762-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
763                <action android:name="android.intent.action.VIEW" />
763-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
763-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
764
765                <category android:name="android.intent.category.DEFAULT" />
765-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
765-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
766                <category android:name="android.intent.category.BROWSABLE" />
766-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
766-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
767
768                <data
768-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
769                    android:host="complete"
769-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
770                    android:path="/com.nafiss.user"
770-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
771                    android:scheme="link-popup" />
771-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
772            </intent-filter>
773        </activity>
774        <activity
774-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:8:9-11:69
775            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
775-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:9:13-80
776            android:exported="false"
776-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:10:13-37
777            android:theme="@style/StripePaymentSheetDefaultTheme" />
777-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:11:13-66
778        <activity
778-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:15:9-18:57
779            android:name="com.stripe.android.view.AddPaymentMethodActivity"
779-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:16:13-76
780            android:exported="false"
780-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:17:13-37
781            android:theme="@style/StripeDefaultTheme" />
781-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:18:13-54
782        <activity
782-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:19:9-22:57
783            android:name="com.stripe.android.view.PaymentMethodsActivity"
783-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:20:13-74
784            android:exported="false"
784-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:21:13-37
785            android:theme="@style/StripeDefaultTheme" />
785-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:22:13-54
786        <activity
786-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:23:9-26:57
787            android:name="com.stripe.android.view.PaymentFlowActivity"
787-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:24:13-71
788            android:exported="false"
788-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:25:13-37
789            android:theme="@style/StripeDefaultTheme" />
789-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:26:13-54
790        <activity
790-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:27:9-30:57
791            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
791-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:28:13-78
792            android:exported="false"
792-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:29:13-37
793            android:theme="@style/StripeDefaultTheme" />
793-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:30:13-54
794        <activity
794-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:31:9-34:61
795            android:name="com.stripe.android.view.PaymentRelayActivity"
795-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:32:13-72
796            android:exported="false"
796-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:33:13-37
797            android:theme="@style/StripeTransparentTheme" />
797-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:34:13-58
798        <!--
799        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
800        launched the browser Activity will also handle the return URL deep link.
801        -->
802        <activity
802-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:40:9-44:61
803            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
803-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:41:13-85
804            android:exported="false"
804-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:42:13-37
805            android:launchMode="singleTask"
805-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:43:13-44
806            android:theme="@style/StripeTransparentTheme" />
806-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:44:13-58
807        <activity
807-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:45:9-62:20
808            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
808-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:46:13-88
809            android:exported="true"
809-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:47:13-36
810            android:launchMode="singleTask"
810-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:48:13-44
811            android:theme="@style/StripeTransparentTheme" >
811-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:49:13-58
812            <intent-filter>
812-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
813                <action android:name="android.intent.action.VIEW" />
813-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
813-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
814
815                <category android:name="android.intent.category.DEFAULT" />
815-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
815-->C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
816                <category android:name="android.intent.category.BROWSABLE" />
816-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
816-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
817
818                <!-- Must match `DefaultReturnUrl#value`. -->
819                <data
819-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
820                    android:host="payment_return_url"
820-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
821                    android:path="/com.nafiss.user"
821-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
822                    android:scheme="stripesdk" />
822-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
823            </intent-filter>
824        </activity>
825        <activity
825-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:63:9-66:57
826            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
826-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:64:13-114
827            android:exported="false"
827-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:65:13-37
828            android:theme="@style/StripeDefaultTheme" />
828-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:66:13-54
829        <activity
829-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:67:9-70:66
830            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
830-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:68:13-90
831            android:exported="false"
831-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:69:13-37
832            android:theme="@style/StripeGooglePayDefaultTheme" />
832-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:70:13-63
833        <activity
833-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:71:9-74:66
834            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
834-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:72:13-103
835            android:exported="false"
835-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:73:13-37
836            android:theme="@style/StripeGooglePayDefaultTheme" />
836-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:74:13-63
837        <activity
837-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:75:9-78:68
838            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
838-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:76:13-107
839            android:exported="false"
839-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:77:13-37
840            android:theme="@style/StripePayLauncherDefaultTheme" />
840-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:78:13-65
841        <activity
841-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:79:9-82:61
842            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
842-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:80:13-97
843            android:exported="false"
843-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:81:13-37
844            android:theme="@style/StripeTransparentTheme" />
844-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:82:13-58
845        <activity
845-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
846            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
846-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
847            android:exported="false"
847-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
848            android:theme="@style/Stripe3DS2Theme" />
848-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
849
850        <service
850-->[:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
851            android:name="com.baseflow.geolocator.GeolocatorLocationService"
851-->[:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
852            android:enabled="true"
852-->[:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
853            android:exported="false"
853-->[:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
854            android:foregroundServiceType="location" />
854-->[:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
855
856        <provider
856-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
857            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
857-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
858            android:authorities="com.nafiss.user.flutter.image_provider"
858-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
859            android:exported="false"
859-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
860            android:grantUriPermissions="true" >
860-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
861            <meta-data
861-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
862                android:name="android.support.FILE_PROVIDER_PATHS"
862-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
863                android:resource="@xml/flutter_image_picker_file_paths" />
863-->[:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
864        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
865        <service
865-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
866            android:name="com.google.android.gms.metadata.ModuleDependencies"
866-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
867            android:enabled="false"
867-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
868            android:exported="false" >
868-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
869            <intent-filter>
869-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
870                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
870-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
870-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
871            </intent-filter>
872
873            <meta-data
873-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
874                android:name="photopicker_activity:0:required"
874-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
875                android:value="" />
875-->[:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
876        </service>
877
878        <activity
878-->[:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
879            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
879-->[:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
880            android:exported="false"
880-->[:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
881            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
881-->[:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
882        <activity
882-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:46:9-52:63
883            android:name="com.journeyapps.barcodescanner.CaptureActivity"
883-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:47:13-74
884            android:clearTaskOnLaunch="true"
884-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:48:13-45
885            android:screenOrientation="sensorLandscape"
885-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:49:13-56
886            android:stateNotNeeded="true"
886-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:50:13-42
887            android:theme="@style/zxing_CaptureTheme"
887-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:51:13-54
888            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- Needs to be explicitly declared on P+ -->
888-->[com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:52:13-60
889        <uses-library
889-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
890            android:name="org.apache.http.legacy"
890-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
891            android:required="false" />
891-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
892
893        <service
893-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
894            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
894-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
895            android:enabled="true"
895-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
896            android:exported="false" >
896-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
897            <meta-data
897-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
898                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
898-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
899                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
899-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
900        </service>
901
902        <activity
902-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
903            android:name="androidx.credentials.playservices.HiddenActivity"
903-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
904            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
904-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
905            android:enabled="true"
905-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
906            android:exported="false"
906-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
907            android:fitsSystemWindows="true"
907-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
908            android:theme="@style/Theme.Hidden" >
908-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
909        </activity>
910        <activity
910-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
911            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
911-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
912            android:excludeFromRecents="true"
912-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
913            android:exported="false"
913-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
914            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
914-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
915        <!--
916            Service handling Google Sign-In user revocation. For apps that do not integrate with
917            Google Sign-In, this service will never be started.
918        -->
919        <service
919-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
920            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
920-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
921            android:exported="true"
921-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
922            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
922-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
923            android:visibleToInstantApps="true" />
923-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
924
925        <receiver
925-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
926            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
926-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
927            android:enabled="true"
927-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
928            android:exported="false" >
928-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
929        </receiver>
930
931        <service
931-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
932            android:name="com.google.android.gms.measurement.AppMeasurementService"
932-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
933            android:enabled="true"
933-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
934            android:exported="false" />
934-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
935        <service
935-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
936            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
936-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
937            android:enabled="true"
937-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
938            android:exported="false"
938-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
939            android:permission="android.permission.BIND_JOB_SERVICE" />
939-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
940
941        <provider
941-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
942            android:name="androidx.startup.InitializationProvider"
942-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
943            android:authorities="com.nafiss.user.androidx-startup"
943-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
944            android:exported="false" >
944-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
945            <meta-data
945-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
946                android:name="androidx.emoji2.text.EmojiCompatInitializer"
946-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
947                android:value="androidx.startup" />
947-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
948            <meta-data
948-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
949                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
949-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
950                android:value="androidx.startup" />
950-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
951            <meta-data
951-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
952                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
952-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
953                android:value="androidx.startup" />
953-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
954        </provider>
955
956        <uses-library
956-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
957            android:name="androidx.window.extensions"
957-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
958            android:required="false" />
958-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
959        <uses-library
959-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
960            android:name="androidx.window.sidecar"
960-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
961            android:required="false" />
961-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
962        <uses-library
962-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
963            android:name="android.ext.adservices"
963-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
964            android:required="false" />
964-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
965
966        <activity
966-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
967            android:name="com.google.android.gms.common.api.GoogleApiActivity"
967-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
968            android:exported="false"
968-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
969            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
969-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
970
971        <meta-data
971-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
972            android:name="com.google.android.gms.version"
972-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
973            android:value="@integer/google_play_services_version" />
973-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
974
975        <service
975-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
976            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
976-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
977            android:exported="false" >
977-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
978            <meta-data
978-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
979                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
979-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
980                android:value="cct" />
980-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
981        </service>
982        <service
982-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
983            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
983-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
984            android:exported="false"
984-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
985            android:permission="android.permission.BIND_JOB_SERVICE" >
985-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
986        </service>
987
988        <receiver
988-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
989            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
989-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
990            android:exported="false" />
990-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
991        <receiver
991-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
992            android:name="androidx.profileinstaller.ProfileInstallReceiver"
992-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
993            android:directBootAware="false"
993-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
994            android:enabled="true"
994-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
995            android:exported="true"
995-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
996            android:permission="android.permission.DUMP" >
996-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
997            <intent-filter>
997-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
998                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
998-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
998-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
999            </intent-filter>
1000            <intent-filter>
1000-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
1001                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
1001-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
1001-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
1002            </intent-filter>
1003            <intent-filter>
1003-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
1004                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
1004-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
1004-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
1005            </intent-filter>
1006            <intent-filter>
1006-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
1007                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
1007-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
1007-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
1008            </intent-filter>
1009        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
1010        <activity
1010-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
1011            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
1011-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
1012            android:exported="false"
1012-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
1013            android:stateNotNeeded="true"
1013-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
1014            android:theme="@style/Theme.PlayCore.Transparent" />
1014-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
1015
1016        <meta-data
1016-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
1017            android:name="aia-compat-api-min-version"
1017-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
1018            android:value="1" />
1018-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
1019    </application>
1020
1021</manifest>
