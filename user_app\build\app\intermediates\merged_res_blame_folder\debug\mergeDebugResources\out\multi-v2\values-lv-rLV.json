{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-lv-rLV/values-lv-rLV.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,244,334,415,495,623,722,823,914,1033,1203,1394,1538,1630,1735,1830,1927,2112,2217,2320,2575,2676,2870,2967,3029,3097,3178,3272,3370,3479,3669,3738,3807,3884,3968,4053,4124,4208,4280,4357,4484,4568,4672,4766,4839,4919,4986,5073,5135,5240,5363,5466,5589,5653,5747,5854,5924,6028,6099,6182,6258,6371,6496,6584", "endColumns": "89,98,89,80,79,127,98,100,90,118,169,190,143,91,104,94,96,184,104,102,254,100,193,96,61,67,80,93,97,108,189,68,68,76,83,84,70,83,71,76,126,83,103,93,72,79,66,86,61,104,122,102,122,63,93,106,69,103,70,82,75,112,124,87,58", "endOffsets": "140,239,329,410,490,618,717,818,909,1028,1198,1389,1533,1625,1730,1825,1922,2107,2212,2315,2570,2671,2865,2962,3024,3092,3173,3267,3365,3474,3664,3733,3802,3879,3963,4048,4119,4203,4275,4352,4479,4563,4667,4761,4834,4914,4981,5068,5130,5235,5358,5461,5584,5648,5742,5849,5919,6023,6094,6177,6253,6366,6491,6579,6638"}, "to": {"startLines": "17,88,100,101,102,119,130,132,133,182,189,190,191,194,196,198,199,200,201,202,203,204,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,251,267,276,277,278,279,280,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1160,7049,8892,8982,9063,11045,12077,12225,12326,16699,17243,17413,17604,17904,18099,18299,18394,18491,18676,18781,18884,19139,19330,19524,19621,19683,19751,19832,19926,20024,20133,20323,20392,20461,20538,20622,20707,20778,20862,20934,21011,21569,21653,21757,21851,21924,22004,22071,22158,22220,22325,22448,22551,22674,22738,22832,22939,23523,26050,26673,26756,26832,26945,27070,27962", "endColumns": "89,98,89,80,79,127,98,100,90,118,169,190,143,91,104,94,96,184,104,102,254,100,193,96,61,67,80,93,97,108,189,68,68,76,83,84,70,83,71,76,126,83,103,93,72,79,66,86,61,104,122,102,122,63,93,106,69,103,70,82,75,112,124,87,58", "endOffsets": "1245,7143,8977,9058,9138,11168,12171,12321,12412,16813,17408,17599,17743,17991,18199,18389,18486,18671,18776,18879,19134,19235,19519,19616,19678,19746,19827,19921,20019,20128,20318,20387,20456,20533,20617,20702,20773,20857,20929,21006,21133,21648,21752,21846,21919,21999,22066,22153,22215,22320,22443,22546,22669,22733,22827,22934,23004,23622,26116,26751,26827,26940,27065,27153,28016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,246,312,379,443,520", "endColumns": "87,102,65,66,63,76,65", "endOffsets": "138,241,307,374,438,515,581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,199,263,349,414,488,557,634,709,776,844", "endColumns": "81,61,63,85,64,73,68,76,74,66,67,71", "endOffsets": "132,194,258,344,409,483,552,629,704,771,839,911"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1685,2525,2673,2737,2823,3115,3703,3907,4162,4504,4874,5196", "endColumns": "81,61,63,85,64,73,68,76,74,66,67,71", "endOffsets": "1762,2582,2732,2818,2883,3184,3767,3979,4232,4566,4937,5263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,201,269,362,432,493,566,631,697,781,851,924,985,1059,1145,1215,1280,1357,1427,1519,1599,1680,1767,1869,1950,1999,2048,2127,2189,2272,2345,2447,2531,2631", "endColumns": "63,81,67,92,69,60,72,64,65,83,69,72,60,73,85,69,64,76,69,91,79,80,86,101,80,48,48,78,61,82,72,101,83,99,95", "endOffsets": "114,196,264,357,427,488,561,626,692,776,846,919,980,1054,1140,1210,1275,1352,1422,1514,1594,1675,1762,1864,1945,1994,2043,2122,2184,2267,2340,2442,2526,2626,2722"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,129,140,141,143,150,151,160,161,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1621,1873,2150,2301,2394,2464,2977,3050,3189,3255,3339,3409,3482,3543,3617,3772,3842,4085,5034,5104,6143,6303,6384,10807,10909,12028,12830,12879,13043,13825,13908,14623,14725,15427,15527", "endColumns": "63,81,67,92,69,60,72,64,65,83,69,72,60,73,85,69,64,76,69,91,79,80,86,101,80,48,48,78,61,82,72,101,83,99,95", "endOffsets": "1680,1950,2213,2389,2459,2520,3045,3110,3250,3334,3404,3477,3538,3612,3698,3837,3902,4157,5099,5191,6218,6379,6466,10904,10985,12072,12874,12953,13100,13903,13976,14720,14804,15522,15618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,332,426,628,676,744,836,908,1133,1204,1299,1366,1421,1485,1748,1822,1888,1973,2027,2107,2226,2323,2381,2465,2545,2630,2696,2801,3073,3149,3227,3302,3363,3426,3487,3560,3643,3746,3841,3931,4031,4123,4197,4276,4362,4552,4751,4863,4991,5049,5760,5823", "endColumns": "118,157,93,201,47,67,91,71,224,70,94,66,54,63,262,73,65,84,53,79,118,96,57,83,79,84,65,104,271,75,77,74,60,62,60,72,82,102,94,89,99,91,73,78,85,189,198,111,127,57,710,62,54", "endOffsets": "169,327,421,623,671,739,831,903,1128,1199,1294,1361,1416,1480,1743,1817,1883,1968,2022,2102,2221,2318,2376,2460,2540,2625,2691,2796,3068,3144,3222,3297,3358,3421,3482,3555,3638,3741,3836,3926,4026,4118,4192,4271,4357,4547,4746,4858,4986,5044,5755,5818,5873"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,128,134,135,142,152,155,156,157,158,159,165,168,173,175,176,177,178,181,183,184,188,192,193,195,197,205,225,226,227,228,229,246,253,254,255,256,258,259,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6678,6797,6955,7148,8221,8269,8337,8429,8501,8726,8797,10740,10990,11173,11765,12417,12491,12958,13981,14185,14265,14384,14481,14539,15101,15342,15789,15929,16034,16306,16382,16624,16818,16879,17182,17748,17821,17996,18204,19240,21138,21238,21330,21404,21483,23009,23681,23880,23992,24120,24249,24960,26618", "endColumns": "118,157,93,201,47,67,91,71,224,70,94,66,54,63,262,73,65,84,53,79,118,96,57,83,79,84,65,104,271,75,77,74,60,62,60,72,82,102,94,89,99,91,73,78,85,189,198,111,127,57,710,62,54", "endOffsets": "6792,6950,7044,7345,8264,8332,8424,8496,8721,8792,8887,10802,11040,11232,12023,12486,12552,13038,14030,14260,14379,14476,14534,14618,15176,15422,15850,16029,16301,16377,16455,16694,16874,16937,17238,17816,17899,18094,18294,19325,21233,21325,21399,21478,21564,23194,23875,23987,24115,24173,24955,25018,26668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,210,342,406,470,563,622,683,763,862,1037,1301,1590,1666,1738,1826,1913,2035,2170,2235,2311,2472,2542,2604,2677,2753,2873,2989,3093,3182,3263,3343,3414", "endColumns": "74,79,131,63,63,92,58,60,79,98,174,263,288,75,71,87,86,121,134,64,75,160,69,61,72,75,119,115,103,88,80,79,70,182", "endOffsets": "125,205,337,401,465,558,617,678,758,857,1032,1296,1585,1661,1733,1821,1908,2030,2165,2230,2306,2467,2537,2599,2672,2748,2868,2984,3088,3177,3258,3338,3409,3592"}, "to": {"startLines": "16,18,162,180,185,247,260,261,262,263,264,265,266,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,299,300,301,302,303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1085,1250,14809,16560,16942,23199,25023,25082,25143,25223,25322,25497,25761,27158,27234,27306,27394,27481,27603,27738,27803,28021,28182,28252,28314,28387,28463,28583,28699,28803,28892,28973,29053,29124", "endColumns": "74,79,131,63,63,92,58,60,79,98,174,263,288,75,71,87,86,121,134,64,75,160,69,61,72,75,119,115,103,88,80,79,70,182", "endOffsets": "1155,1325,14936,16619,17001,23287,25077,25138,25218,25317,25492,25756,26045,27229,27301,27389,27476,27598,27733,27798,27874,28177,28247,28309,28382,28458,28578,28694,28798,28887,28968,29048,29119,29302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-lv-rLV\\values-lv-rLV.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,342,425,488,554,609,683,769,845,951,1057,1146,1229,1315,1404,1505,1604,1676,1772,1863,1957,2075,2167,2263,2342,2443,2526,2615,2704,2789,2868,2974,3042,3122,3229,3329,3401,3466,4200,4884,4959,5074,5168,5223,5314,5400,5468,5556,5643,5710,5797,5846,5924,6030,6100,6192,6259,6325,6374,6454,6552,6599,6647,6731,6789,6860,7062,7233,7367,7434,7517,7588,7677,7758,7838,7915,8004,8078,8178,8264,8354,8410,8536,8585,8639,8710,8782,8855,8922,8995,9084,9154,9207", "endColumns": "70,79,61,73,82,62,65,54,73,85,75,105,105,88,82,85,88,100,98,71,95,90,93,117,91,95,78,100,82,88,88,84,78,105,67,79,106,99,71,64,733,683,74,114,93,54,90,85,67,87,86,66,86,48,77,105,69,91,66,65,48,79,97,46,47,83,57,70,201,170,133,66,82,70,88,80,79,76,88,73,99,85,89,55,125,48,53,70,71,72,66,72,88,69,52,82", "endOffsets": "121,201,263,337,420,483,549,604,678,764,840,946,1052,1141,1224,1310,1399,1500,1599,1671,1767,1858,1952,2070,2162,2258,2337,2438,2521,2610,2699,2784,2863,2969,3037,3117,3224,3324,3396,3461,4195,4879,4954,5069,5163,5218,5309,5395,5463,5551,5638,5705,5792,5841,5919,6025,6095,6187,6254,6320,6369,6449,6547,6594,6642,6726,6784,6855,7057,7228,7362,7429,7512,7583,7672,7753,7833,7910,7999,8073,8173,8259,8349,8405,8531,8580,8634,8705,8777,8850,8917,8990,9079,9149,9202,9285"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,124,125,126,127,131,136,137,138,139,144,145,146,147,148,149,153,154,163,164,166,167,171,172,174,179,186,187,248,249,250,252,257,268,269,270,271,272,273,274,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "586,657,737,799,873,956,1019,1330,1385,1459,1545,1767,1955,2061,2218,2587,2888,3984,4237,4336,4408,4571,4662,4756,4942,5268,5364,5443,5544,5627,5716,5805,5890,5969,6075,6223,6471,6578,7350,7422,7487,9143,9827,9902,10017,10111,10166,10257,10343,10411,10499,10586,10653,11237,11286,11364,11470,11540,11632,11699,12176,12557,12637,12735,12782,13105,13189,13247,13318,13520,13691,14035,14102,14941,15012,15181,15262,15623,15700,15855,16460,17006,17092,23292,23348,23474,23627,24178,26121,26193,26266,26333,26406,26495,26565,27879", "endColumns": "70,79,61,73,82,62,65,54,73,85,75,105,105,88,82,85,88,100,98,71,95,90,93,117,91,95,78,100,82,88,88,84,78,105,67,79,106,99,71,64,733,683,74,114,93,54,90,85,67,87,86,66,86,48,77,105,69,91,66,65,48,79,97,46,47,83,57,70,201,170,133,66,82,70,88,80,79,76,88,73,99,85,89,55,125,48,53,70,71,72,66,72,88,69,52,82", "endOffsets": "652,732,794,868,951,1014,1080,1380,1454,1540,1616,1868,2056,2145,2296,2668,2972,4080,4331,4403,4499,4657,4751,4869,5029,5359,5438,5539,5622,5711,5800,5885,5964,6070,6138,6298,6573,6673,7417,7482,8216,9822,9897,10012,10106,10161,10252,10338,10406,10494,10581,10648,10735,11281,11359,11465,11535,11627,11694,11760,12220,12632,12730,12777,12825,13184,13242,13313,13515,13686,13820,14097,14180,15007,15096,15257,15337,15695,15784,15924,16555,17087,17177,23343,23469,23518,23676,24244,26188,26261,26328,26401,26490,26560,26613,27957"}}]}]}