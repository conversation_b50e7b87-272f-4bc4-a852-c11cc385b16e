{"logs": [{"outputFile": "com.nafiss.user.app-mergeDebugResources-120:/values-ro-rRO/values-ro-rRO.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\be453dfb4e4d01307d43ad1e000cbe50\\transformed\\jetified-link-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,209,358,424,489,578,637,701,802,905,1101,1399,1724,1812,1890,1977,2072,2208,2346,2416,2487,2661,2727,2786,2859,2947,3104,3220,3325,3412,3489,3588,3659", "endColumns": "72,80,148,65,64,88,58,63,100,102,195,297,324,87,77,86,94,135,137,69,70,173,65,58,72,87,156,115,104,86,76,98,70,153", "endOffsets": "123,204,353,419,484,573,632,696,797,900,1096,1394,1719,1807,1885,1972,2067,2203,2341,2411,2482,2656,2722,2781,2854,2942,3099,3215,3320,3407,3484,3583,3654,3808"}, "to": {"startLines": "16,18,162,180,185,247,260,261,262,263,264,265,266,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,299,300,301,302,303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1081,1244,15030,16962,17340,23800,25745,25804,25868,25969,26072,26268,26566,28010,28098,28176,28263,28358,28494,28632,28702,28915,29089,29155,29214,29287,29375,29532,29648,29753,29840,29917,30016,30087", "endColumns": "72,80,148,65,64,88,58,63,100,102,195,297,324,87,77,86,94,135,137,69,70,173,65,58,72,87,156,115,104,86,76,98,70,153", "endOffsets": "1149,1320,15174,17023,17400,23884,25799,25863,25964,26067,26263,26561,26886,28093,28171,28258,28353,28489,28627,28697,28768,29084,29150,29209,29282,29370,29527,29643,29748,29835,29912,30011,30082,30236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dfc180ed60618bd5d6705514414511e1\\transformed\\jetified-stripe-core-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,200,262,346,408,480,539,618,690,758,818", "endColumns": "85,58,61,83,61,71,58,78,71,67,59,68", "endOffsets": "136,195,257,341,403,475,534,613,685,753,813,882"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1719,2554,2692,2754,2838,3121,3654,3846,4098,4420,4752,5038", "endColumns": "85,58,61,83,61,71,58,78,71,67,59,68", "endOffsets": "1800,2608,2749,2833,2895,3188,3708,3920,4165,4483,4807,5102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\894450a07eca60b3fddb3d8577a359fa\\transformed\\jetified-stripe-ui-core-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,205,272,357,427,488,560,627,689,757,827,887,950,1024,1088,1158,1221,1295,1360,1441,1519,1607,1701,1813,1903,1956,2004,2085,2147,2222,2291,2400,2486,2591", "endColumns": "63,85,66,84,69,60,71,66,61,67,69,59,62,73,63,69,62,73,64,80,77,87,93,111,89,52,47,80,61,74,68,108,85,104,103", "endOffsets": "114,200,267,352,422,483,555,622,684,752,822,882,945,1019,1083,1153,1216,1290,1355,1436,1514,1602,1696,1808,1898,1951,1999,2080,2142,2217,2286,2395,2481,2586,2690"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,129,140,141,143,150,151,160,161,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1655,1911,2187,2338,2423,2493,2982,3054,3193,3255,3323,3393,3453,3516,3590,3713,3783,4024,4892,4957,6034,6205,6293,10924,11036,12204,13038,13086,13221,13960,14035,14835,14944,15728,15833", "endColumns": "63,85,66,84,69,60,71,66,61,67,69,59,62,73,63,69,62,73,64,80,77,87,93,111,89,52,47,80,61,74,68,108,85,104,103", "endOffsets": "1714,1992,2249,2418,2488,2549,3049,3116,3250,3318,3388,3448,3511,3585,3649,3778,3841,4093,4952,5033,6107,6288,6382,11031,11121,12252,13081,13162,13278,14030,14099,14939,15025,15828,15932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13e3f273c1cfa96f815ed8247f020f96\\transformed\\jetified-payments-ui-core-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,345,439,638,685,751,856,927,1154,1219,1319,1390,1444,1508,1809,1886,1954,2008,2061,2163,2308,2439,2496,2585,2669,2756,2821,2936,3237,3325,3411,3478,3539,3603,3664,3735,3814,3924,4024,4118,4216,4308,4387,4466,4551,4738,4934,5046,5178,5236,6030,6096", "endColumns": "128,160,93,198,46,65,104,70,226,64,99,70,53,63,300,76,67,53,52,101,144,130,56,88,83,86,64,114,300,87,85,66,60,63,60,70,78,109,99,93,97,91,78,78,84,186,195,111,131,57,793,65,54", "endOffsets": "179,340,434,633,680,746,851,922,1149,1214,1314,1385,1439,1503,1804,1881,1949,2003,2056,2158,2303,2434,2491,2580,2664,2751,2816,2931,3232,3320,3406,3473,3534,3598,3659,3730,3809,3919,4019,4113,4211,4303,4382,4461,4546,4733,4929,5041,5173,5231,6025,6091,6146"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,128,134,135,142,152,155,156,157,158,159,165,168,173,175,176,177,178,181,183,184,188,192,193,195,197,205,225,226,227,228,229,246,253,254,255,256,258,259,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6648,6777,6938,7141,8205,8252,8318,8423,8494,8721,8786,10853,11126,11323,11903,12618,12695,13167,14104,14311,14413,14558,14689,14746,15366,15641,16117,16264,16379,16680,16768,17028,17215,17276,17580,18197,18268,18438,18662,19758,21712,21810,21902,21981,22060,23613,24316,24512,24624,24756,24885,25679,27457", "endColumns": "128,160,93,198,46,65,104,70,226,64,99,70,53,63,300,76,67,53,52,101,144,130,56,88,83,86,64,114,300,87,85,66,60,63,60,70,78,109,99,93,97,91,78,78,84,186,195,111,131,57,793,65,54", "endOffsets": "6772,6933,7027,7335,8247,8313,8418,8489,8716,8781,8881,10919,11175,11382,12199,12690,12758,13216,14152,14408,14553,14684,14741,14830,15445,15723,16177,16374,16675,16763,16849,17090,17271,17335,17636,18263,18342,18543,18757,19847,21805,21897,21976,22055,22140,23795,24507,24619,24751,24809,25674,25740,27507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\590721c84d64afcb0498097bcbbfea03\\transformed\\jetified-stripe-3ds2-android-6.1.8\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,140,241,307,374,439,516", "endColumns": "84,100,65,66,64,76,70", "endOffsets": "135,236,302,369,434,511,582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cfef1a648544242e02cb64eb79d84a02\\transformed\\jetified-paymentsheet-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,254,346,426,505,648,757,862,958,1078,1260,1471,1634,1725,1839,1933,2034,2240,2348,2445,2728,2835,3041,3137,3201,3268,3354,3451,3549,3654,3866,3935,4001,4077,4170,4275,4344,4428,4501,4576,4695,4786,4894,4992,5067,5151,5222,5312,5373,5478,5588,5691,5823,5895,5991,6092,6163,6274,6350,6445,6514,6632,6762,6848", "endColumns": "89,108,91,79,78,142,108,104,95,119,181,210,162,90,113,93,100,205,107,96,282,106,205,95,63,66,85,96,97,104,211,68,65,75,92,104,68,83,72,74,118,90,107,97,74,83,70,89,60,104,109,102,131,71,95,100,70,110,75,94,68,117,129,85,66", "endOffsets": "140,249,341,421,500,643,752,857,953,1073,1255,1466,1629,1720,1834,1928,2029,2235,2343,2440,2723,2830,3036,3132,3196,3263,3349,3446,3544,3649,3861,3930,3996,4072,4165,4270,4339,4423,4496,4571,4690,4781,4889,4987,5062,5146,5217,5307,5368,5473,5583,5686,5818,5890,5986,6087,6158,6269,6345,6440,6509,6627,6757,6843,6910"}, "to": {"startLines": "17,88,100,101,102,119,130,132,133,182,189,190,191,194,196,198,199,200,201,202,203,204,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,251,267,276,277,278,279,280,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,7032,8886,8978,9058,11180,12257,12417,12522,17095,17641,17823,18034,18347,18548,18762,18856,18957,19163,19271,19368,19651,19852,20058,20154,20218,20285,20371,20468,20566,20671,20883,20952,21018,21094,21187,21292,21361,21445,21518,21593,22145,22236,22344,22442,22517,22601,22672,22762,22823,22928,23038,23141,23273,23345,23441,23542,24143,26891,27512,27607,27676,27794,27924,28848", "endColumns": "89,108,91,79,78,142,108,104,95,119,181,210,162,90,113,93,100,205,107,96,282,106,205,95,63,66,85,96,97,104,211,68,65,75,92,104,68,83,72,74,118,90,107,97,74,83,70,89,60,104,109,102,131,71,95,100,70,110,75,94,68,117,129,85,66", "endOffsets": "1239,7136,8973,9053,9132,11318,12361,12517,12613,17210,17818,18029,18192,18433,18657,18851,18952,19158,19266,19363,19646,19753,20053,20149,20213,20280,20366,20463,20561,20666,20878,20947,21013,21089,21182,21287,21356,21440,21513,21588,21707,22231,22339,22437,22512,22596,22667,22757,22818,22923,23033,23136,23268,23340,23436,23537,23608,24249,26962,27602,27671,27789,27919,28005,28910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fa82a6bcb6286a780b7683af7fc69878\\transformed\\jetified-payments-core-20.52.3\\res\\values-ro-rRO\\values-ro-rRO.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,340,423,483,549,608,700,784,879,985,1091,1175,1259,1338,1420,1519,1611,1680,1769,1857,1944,2033,2113,2202,2278,2374,2455,2556,2652,2744,2843,2964,3040,3133,3267,3394,3466,3530,4259,4972,5045,5174,5280,5341,5455,5564,5634,5733,5832,5889,5975,6025,6106,6208,6284,6358,6425,6491,6542,6624,6720,6769,6817,6887,6945,7016,7196,7355,7494,7559,7648,7725,7835,7927,8026,8107,8206,8288,8396,8480,8571,8636,8773,8825,8887,8958,9024,9096,9162,9234,9324,9394,9448", "endColumns": "67,76,65,73,82,59,65,58,91,83,94,105,105,83,83,78,81,98,91,68,88,87,86,88,79,88,75,95,80,100,95,91,98,120,75,92,133,126,71,63,728,712,72,128,105,60,113,108,69,98,98,56,85,49,80,101,75,73,66,65,50,81,95,48,47,69,57,70,179,158,138,64,88,76,109,91,98,80,98,81,107,83,90,64,136,51,61,70,65,71,65,71,89,69,53,74", "endOffsets": "118,195,261,335,418,478,544,603,695,779,874,980,1086,1170,1254,1333,1415,1514,1606,1675,1764,1852,1939,2028,2108,2197,2273,2369,2450,2551,2647,2739,2838,2959,3035,3128,3262,3389,3461,3525,4254,4967,5040,5169,5275,5336,5450,5559,5629,5728,5827,5884,5970,6020,6101,6203,6279,6353,6420,6486,6537,6619,6715,6764,6812,6882,6940,7011,7191,7350,7489,7554,7643,7720,7830,7922,8021,8102,8201,8283,8391,8475,8566,8631,8768,8820,8882,8953,9019,9091,9157,9229,9319,9389,9443,9518"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,124,125,126,127,131,136,137,138,139,144,145,146,147,148,149,153,154,163,164,166,167,171,172,174,179,186,187,248,249,250,252,257,268,269,270,271,272,273,274,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "587,655,732,798,872,955,1015,1325,1384,1476,1560,1805,1997,2103,2254,2613,2900,3925,4170,4262,4331,4488,4576,4663,4812,5107,5196,5272,5368,5449,5550,5646,5738,5837,5958,6112,6387,6521,7340,7412,7476,9137,9850,9923,10052,10158,10219,10333,10442,10512,10611,10710,10767,11387,11437,11518,11620,11696,11770,11837,12366,12763,12845,12941,12990,13283,13353,13411,13482,13662,13821,14157,14222,15179,15256,15450,15542,15937,16018,16182,16854,17405,17489,23889,23954,24091,24254,24814,26967,27033,27105,27171,27243,27333,27403,28773", "endColumns": "67,76,65,73,82,59,65,58,91,83,94,105,105,83,83,78,81,98,91,68,88,87,86,88,79,88,75,95,80,100,95,91,98,120,75,92,133,126,71,63,728,712,72,128,105,60,113,108,69,98,98,56,85,49,80,101,75,73,66,65,50,81,95,48,47,69,57,70,179,158,138,64,88,76,109,91,98,80,98,81,107,83,90,64,136,51,61,70,65,71,65,71,89,69,53,74", "endOffsets": "650,727,793,867,950,1010,1076,1379,1471,1555,1650,1906,2098,2182,2333,2687,2977,4019,4257,4326,4415,4571,4658,4747,4887,5191,5267,5363,5444,5545,5641,5733,5832,5953,6029,6200,6516,6643,7407,7471,8200,9845,9918,10047,10153,10214,10328,10437,10507,10606,10705,10762,10848,11432,11513,11615,11691,11765,11832,11898,12412,12840,12936,12985,13033,13348,13406,13477,13657,13816,13955,14217,14306,15251,15361,15537,15636,16013,16112,16259,16957,17484,17575,23949,24086,24138,24311,24880,27028,27100,27166,27238,27328,27398,27452,28843"}}]}]}