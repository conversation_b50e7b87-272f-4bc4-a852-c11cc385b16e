-- Merging decision tree log ---
manifest
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:1:1-97:12
MERGED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:1:1-97:12
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:phonepe_payment_sdk] C:\wamp64\www\user_app\build\phonepe_payment_sdk\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] C:\wamp64\www\user_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:speech_to_text] C:\wamp64\www\user_app\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_android] C:\wamp64\www\user_app\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] C:\wamp64\www\user_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c536f45f7f0e17d74762128d0de08312\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98616cccc02343a36ec5f1315ce8ded1\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:midpay] C:\wamp64\www\user_app\build\midpay\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:2:1-199:12
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:2:1-59:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f63fc8e219d0da2c2381781a446b35d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:2:1-77:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43c94eb5a9be4d13bce209dd2e0f08b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:2:1-14:12
MERGED from [com.akexorcist:localization:1.2.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66e0a3de35d0e8da22dcfd2dcb6b1306\transformed\jetified-localization-1.2.10\AndroidManifest.xml:2:1-9:12
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:2:1-19:12
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6685ae673a600ff3e33abec167a135c\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:2:1-45:12
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:2:1-40:12
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:2:1-85:12
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d20986c655cfa164c65383bfcf47f6d\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:2:1-21:12
MERGED from [com.github.rambler-digital-solutions:swipe-layout-android:1.0.17] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\AndroidManifest.xml:2:1-16:12
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e159e2c46b78cc2c8453cf6e92d6dbbf\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edca040889e79ca9b38ba901c6a0fa7\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d428d89fcf3fdd09e53438e0ac46f10a\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0540263de729c5155ce1a78e37e72993\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5ece3c7dc0bc71837804011c458ad9\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c68162bc9aa811289901f5e22f4653a\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbba42fcad575d7d319036a61be3091b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] C:\wamp64\www\user_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_custom_tabs_android] C:\wamp64\www\user_app\build\flutter_custom_tabs_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_maps_flutter_android] C:\wamp64\www\user_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\wamp64\www\user_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:fluttertoast] C:\wamp64\www\user_app\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] C:\wamp64\www\user_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\wamp64\www\user_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\wamp64\www\user_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\wamp64\www\user_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.droibit:customtabslauncher:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2fefeb540c70b1e1cc75182ca7c52c\transformed\jetified-customtabslauncher-2.0.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24101954538a7f7c2c85ceec583f6b9b\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a6b9eb336c3d2a2816400f2435a9ad9\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b539774c7fd05bf8cab7ad984e61cba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a627de39c21db0a7b56ff671766d4bab\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aec37582522615c6d6916d078a5ccf97\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4dccb56f227b56afcf95779216ccc46\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd5de746ab9f324291c45c4e18d466e0\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c37758e4c632d9b51d3452f8c90e3\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44aaec257ce2cc8cac45c8fbfc80c0\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6d4b9ab294dc37ddcdb38115157bb74\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d238ff801bf4159163d36044ed821d83\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44705c864210af715dcb07f73a96c9ba\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f34f03ecdac974155dc3e7c955360db0\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4a95987497762ed3af885ab116fa264\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc9e89cb1ff62871c83c62856bf97ce5\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\932ba72e17c40aa8556c758dac8079d3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a45c0271b394df3a37de6cccf30579d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5177fdbad1250f9870a2fbb337983ac8\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c79ad1cc2e89996ad18f6440f6c925\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4940c476a92c0e1ce6707ed6d5a3fab1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7643d420cb7f0d645ee669af01e246f4\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9744ea7d593db4848481b2dc1ca0ed99\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dad94121f24fbe5d0b95c09c16583fa\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\861b4fa0657264fccfc92d357e99c12c\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6580ba3899d752f0adcab81246e19f4\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17380c18e82b42e87b2d6dfc71715bd4\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20044f6c5f7a971de13e8ca5eb00b3f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f0aab3e896844600ee839129612136c\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b6ac2e46b48ac9a3aa0ffb25549f7d\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d145a5b15b5ab9079a5c742530e158\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6156536e0b56b649058e060194ae134e\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06934b7ee87ba902cc1c97beba47907c\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\734287ca7a5c3cecf4ed3a5d2b4dd2a4\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aeb39eea497f4247ef3d1f00500f89\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e37d8cd2361f3119cb59f8003d0868\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dad88896bac44393d0c09f988f7f33f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f480edc4ef2de9e546b75a25dbb76874\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71741ae3608a014bf71abddc3430cd76\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4017ee38fffd86d5d2bd1c1bd1be11c8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0780b40bae4a82b2ae549deaa1d2d7a\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae06639c40c37163a8dbcecf01e73766\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e029052ced966115f4d17af0300d534\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83284b1ca1734e1de536bf196acf215e\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc5c87a03af947d6c14df0602dee0471\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e49c3ea676e22abc40aa6676849933c4\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591a2f912f2642b7c3ce93cb5c31cb2\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db920e7c324fc81d84865df5ad7793be\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3b47443e0758c24ab25e1a567f9ffd\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aebe4dd8179f1aa94b7c979bec7ed9c5\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4c7bd69a33da4ec1eccd1f1916102f3\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb45c187cc4bf07f326acb55403dced5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0f9724190e07e8737aaa267698c9de2\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e083c77bf2774c552dcb395d84f66a5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0f12582c452eb691a8e44b0e8084bba\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8933da7794439b520a0a7d7c44b67732\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f9f62d475da5647ccd554e09d9a5175\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb23a5c46c3e34a7c7847fc03bf5fc3c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98de64f0111d22d1533700e71a117eb1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97bc3aba17b8e77b2a7cadcc02faec9f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\384a3487ef0a0080c58695a386ab607a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37de469302e2c446aa8c642cc10d3877\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d60b9dfcc626ec6500a5bf1bb597609c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da03bed7a3bc1708d07957d8daac4d73\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2e5b5c6f8d61eda7736195ad612c0c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9ae86c85487d86163dc59b9985eb1b1\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\876e4ff582ebdfeddfd9bca2d49466dd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b160d8b9da1779072233b65dafb67e1\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adbfae24b25496a769a0de168ef10463\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbddd8ac3be61af4933f58e5538a8bdb\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfe8c73686adb669489e2b68431f1479\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e05370dafb4c7ac1c53ece1b10c40dc\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11143826e5a29537e8ebfb75c1f165fa\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8a00346f769e30fcf102afe4b03d0df\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b2a6a802b80116484a4d228fbeb1a77\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d36266a896a5865f523cbd5bb0163b0\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f9e9b6b52770bd44d0b075f702cb31a\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29be1fa7446a69f224d9ea40229e93c\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\260522677f8d78cc30a779d048c78623\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c11d81441550d0689fe1a67fbc28a1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a624f6169bd423a64f257c4af3ec9f18\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44734e41a66c83c09439a2ee65352add\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\297d281f214de12afec0ec6772c222bb\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b577e9390ae7b35b2264246136ac5c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c1b45a514dca3fc75b0b1bcc975036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\809f7685cfffa5bfe4e206b55f99aafe\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad783780d62bb70d3b31349f8da5d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f366e0976d4008642dc16217a8811b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a13a77dd6226e0f03c0adc904d93bab0\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d3e3f1bd8a48a8aaf1db8287bb1341\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe3d372fd19dedb11f4738343e5decc2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d04fca7f6da8dbd8e5a516a5e295a7af\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27abe7c8fa32e05315b9b266c81703d4\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d0c7249b1d25e81d28d5842176966cc\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06aa85e04b36544d68a4bc5515558aa9\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ae0dd42f241a9bccdb2073f11a3660\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a74ca4ad728806d1f5c26d377000afdb\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1027c7af5ad42434485bf3858f32dcf4\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d2725a8f52f2f1b7f5df24dbfcf06b9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82370e92982c022c5183cf441d1dd675\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a530d30d38b7060f780b584f0324ba6b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebdaa7a64fa10d06136ca743315eaa1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb57c3e5a03a182263a89edddce573a7\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55bd33645caf703ea55f1d1f4816c5f8\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54ac26a17e1996f67c0683b84824bbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a54714422927f398c3d7384d486368e\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f543ec7bf2ab950372c7a56e243d0a81\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d9e0c9d265525bc50843502f561d93\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caccccc897ccf3cd2ffaeaa238014d9f\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.chuckerteam.chucker:library-no-op:3.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b27b4ed41d3e76df5dbae1c75e8e9d8e\transformed\jetified-library-no-op-3.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db0890d196505bd23acf50fce22364ef\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3217727316632b44892913484b9635df\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.midtrans:issuetracker:1.12.0-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4618d46e7fa1268b54a29fd46ce2b41c\transformed\jetified-issuetracker-1.12.0-SANDBOX\AndroidManifest.xml:2:1-11:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6a794da9e3a75be32e0bfebe1ec73a5\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c00d41ccda6e3300e348397243de06d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea45bcc7faaae4cfa3a616b9f8202ae6\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [me.saket:better-link-movement-method:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf62539f90a6d2a15f0bacfd65e04f94\transformed\jetified-better-link-movement-method-2.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:2:1-16:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
	package
		ADDED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml:2:5-30
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:30:5-67
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:30:5-67
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:7:5-67
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:12:5-67
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:12:5-67
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:12:5-67
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:12:5-67
MERGED from [:google_sign_in_android] C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:13:5-79
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:13:5-79
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:29:5-79
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:29:5-79
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:11:5-79
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:11:5-79
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:11:5-79
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:11:5-79
MERGED from [:connectivity_plus] C:\wamp64\www\user_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\wamp64\www\user_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6a794da9e3a75be32e0bfebe1ec73a5\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6a794da9e3a75be32e0bfebe1ec73a5\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:6:5-65
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:21:5-65
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:21:5-65
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:9:5-77
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
MERGED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:10:5-70
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:10:22-68
uses-permission#android.permission.BLUETOOTH
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:12:5-73
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:12:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:13:22-73
application
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:15:5-96:19
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c536f45f7f0e17d74762128d0de08312\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c536f45f7f0e17d74762128d0de08312\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98616cccc02343a36ec5f1315ce8ded1\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98616cccc02343a36ec5f1315ce8ded1\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:20:5-197:19
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:20:5-197:19
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:36:5-57:19
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:36:5-57:19
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:20:5-75:19
MERGED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:11:5-12:19
MERGED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:11:5-12:19
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:14:5-17:19
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:14:5-17:19
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:7:5-38:19
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:7:5-38:19
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:14:5-83:19
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:14:5-83:19
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:15:5-19:19
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:15:5-19:19
MERGED from [com.github.rambler-digital-solutions:swipe-layout-android:1.0.17] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\AndroidManifest.xml:12:5-14:47
MERGED from [com.github.rambler-digital-solutions:swipe-layout-android:1.0.17] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\AndroidManifest.xml:12:5-14:47
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbba42fcad575d7d319036a61be3091b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbba42fcad575d7d319036a61be3091b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:45:5-53:19
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:45:5-53:19
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6d4b9ab294dc37ddcdb38115157bb74\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6d4b9ab294dc37ddcdb38115157bb74\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d238ff801bf4159163d36044ed821d83\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d238ff801bf4159163d36044ed821d83\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4a95987497762ed3af885ab116fa264\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4a95987497762ed3af885ab116fa264\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\932ba72e17c40aa8556c758dac8079d3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\932ba72e17c40aa8556c758dac8079d3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a45c0271b394df3a37de6cccf30579d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a45c0271b394df3a37de6cccf30579d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aeb39eea497f4247ef3d1f00500f89\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aeb39eea497f4247ef3d1f00500f89\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfe8c73686adb669489e2b68431f1479\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfe8c73686adb669489e2b68431f1479\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e05370dafb4c7ac1c53ece1b10c40dc\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e05370dafb4c7ac1c53ece1b10c40dc\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8a00346f769e30fcf102afe4b03d0df\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8a00346f769e30fcf102afe4b03d0df\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b2a6a802b80116484a4d228fbeb1a77\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b2a6a802b80116484a4d228fbeb1a77\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d36266a896a5865f523cbd5bb0163b0\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d36266a896a5865f523cbd5bb0163b0\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82370e92982c022c5183cf441d1dd675\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82370e92982c022c5183cf441d1dd675\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55bd33645caf703ea55f1d1f4816c5f8\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55bd33645caf703ea55f1d1f4816c5f8\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
	android:extractNativeLibs
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:18:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:17:9-37
		REJECTED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:11:18-50
		REJECTED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:17:9-41
	android:allowBackup
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:21:9-36
		REJECTED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:15:9-36
		REJECTED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:16:9-35
	android:icon
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:16:9-43
	tools:replace
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:19:9-38
	android:usesCleartextTraffic
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:22:9-45
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:20:9-22:72
	android:resource
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:21:17-87
activity#com.nafiss.user.MainActivity
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:24:9-65:20
	android:launchMode
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:28:13-43
	android:hardwareAccelerated
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:27:13-47
	android:windowSoftInputMode
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:31:13-55
	android:exported
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:29:13-36
	android:configChanges
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:26:13-163
	android:theme
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:30:13-47
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:25:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:36:13-38:57
	android:resource
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:38:17-54
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:37:17-72
meta-data#io.flutter.embedding.android.SplashScreenDrawable
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:45:13-47:66
	android:resource
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:47:17-63
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:46:17-81
intent-filter#action:name:FLUTTER_NOTIFICATION_CLICK+category:name:android.intent.category.DEFAULT
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:49:13-52:29
action#FLUTTER_NOTIFICATION_CLICK
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:50:17-69
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:17-76
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:51:27-73
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:54:13-58:29
action#android.intent.action.MAIN
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:55:17-69
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:55:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:56:17-77
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:56:27-74
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:62:13-64:47
	android:value
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:64:17-45
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:63:17-93
meta-data#com.google.android.gms.wallet.api.enabled
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:81:9-83:36
	android:value
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:83:13-33
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:82:13-69
meta-data#com.google.android.geo.API_KEY
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:85:9-87:51
	android:value
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:87:13-48
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:86:13-58
meta-data#flutterEmbedding
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:90:9-92:33
	android:value
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:92:13-30
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:91:13-44
service#com.nafiss.user.MyNavigationService
ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:93:9-95:56
	android:foregroundServiceType
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:95:13-53
	android:name
		ADDED from C:\wamp64\www\user_app\android\app\src\main\AndroidManifest.xml:94:13-47
uses-sdk
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:nb_utils] C:\wamp64\www\user_app\build\nb_utils\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:phonepe_payment_sdk] C:\wamp64\www\user_app\build\phonepe_payment_sdk\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:phonepe_payment_sdk] C:\wamp64\www\user_app\build\phonepe_payment_sdk\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\wamp64\www\user_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\wamp64\www\user_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\wamp64\www\user_app\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:speech_to_text] C:\wamp64\www\user_app\build\speech_to_text\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\wamp64\www\user_app\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\wamp64\www\user_app\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\wamp64\www\user_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\wamp64\www\user_app\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c536f45f7f0e17d74762128d0de08312\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c536f45f7f0e17d74762128d0de08312\transformed\jetified-firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98616cccc02343a36ec5f1315ce8ded1\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98616cccc02343a36ec5f1315ce8ded1\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:midpay] C:\wamp64\www\user_app\build\midpay\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:midpay] C:\wamp64\www\user_app\build\midpay\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:8:5-11:115
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:8:5-11:115
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:5:5-44
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f63fc8e219d0da2c2381781a446b35d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f63fc8e219d0da2c2381781a446b35d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43c94eb5a9be4d13bce209dd2e0f08b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f43c94eb5a9be4d13bce209dd2e0f08b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.stfalcon-studio:SmsVerifyCatcher:0.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dec03efbb7b2ecc979e328cf251000d5\transformed\jetified-SmsVerifyCatcher-0.3.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.akexorcist:localization:1.2.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66e0a3de35d0e8da22dcfd2dcb6b1306\transformed\jetified-localization-1.2.10\AndroidManifest.xml:5:5-7:41
MERGED from [com.akexorcist:localization:1.2.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66e0a3de35d0e8da22dcfd2dcb6b1306\transformed\jetified-localization-1.2.10\AndroidManifest.xml:5:5-7:41
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [com.midtrans:corekit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35f237442f46d349d8888c42ebc9f930\transformed\jetified-corekit-1.31.1-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6685ae673a600ff3e33abec167a135c\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6685ae673a600ff3e33abec167a135c\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d20986c655cfa164c65383bfcf47f6d\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d20986c655cfa164c65383bfcf47f6d\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.rambler-digital-solutions:swipe-layout-android:1.0.17] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.rambler-digital-solutions:swipe-layout-android:1.0.17] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2293c651a7a501467fadf533222baaa\transformed\jetified-swipe-layout-android-1.0.17\AndroidManifest.xml:8:5-10:41
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e159e2c46b78cc2c8453cf6e92d6dbbf\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e159e2c46b78cc2c8453cf6e92d6dbbf\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edca040889e79ca9b38ba901c6a0fa7\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edca040889e79ca9b38ba901c6a0fa7\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d428d89fcf3fdd09e53438e0ac46f10a\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d428d89fcf3fdd09e53438e0ac46f10a\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0540263de729c5155ce1a78e37e72993\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0540263de729c5155ce1a78e37e72993\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b21ac5fe30d157215f8c70dc88df410\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab6f3f9a6a3b6c943160804bc768402a\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5ece3c7dc0bc71837804011c458ad9\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5ece3c7dc0bc71837804011c458ad9\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c68162bc9aa811289901f5e22f4653a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c68162bc9aa811289901f5e22f4653a\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbba42fcad575d7d319036a61be3091b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbba42fcad575d7d319036a61be3091b\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\wamp64\www\user_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\wamp64\www\user_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_custom_tabs_android] C:\wamp64\www\user_app\build\flutter_custom_tabs_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_custom_tabs_android] C:\wamp64\www\user_app\build\flutter_custom_tabs_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\wamp64\www\user_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\wamp64\www\user_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\wamp64\www\user_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\wamp64\www\user_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\wamp64\www\user_app\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\wamp64\www\user_app\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\wamp64\www\user_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\wamp64\www\user_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\wamp64\www\user_app\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\wamp64\www\user_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\wamp64\www\user_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\wamp64\www\user_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\wamp64\www\user_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\wamp64\www\user_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\wamp64\www\user_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.droibit:customtabslauncher:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2fefeb540c70b1e1cc75182ca7c52c\transformed\jetified-customtabslauncher-2.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.droibit:customtabslauncher:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2fefeb540c70b1e1cc75182ca7c52c\transformed\jetified-customtabslauncher-2.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24101954538a7f7c2c85ceec583f6b9b\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24101954538a7f7c2c85ceec583f6b9b\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ac41f25cefd600ae434811e1d648caa\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a6b9eb336c3d2a2816400f2435a9ad9\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a6b9eb336c3d2a2816400f2435a9ad9\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:19:5-43
MERGED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:19:5-43
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.ion:ion:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddfa7043868b864317f9654db0b7992e\transformed\jetified-ion-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b539774c7fd05bf8cab7ad984e61cba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b539774c7fd05bf8cab7ad984e61cba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a627de39c21db0a7b56ff671766d4bab\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a627de39c21db0a7b56ff671766d4bab\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aec37582522615c6d6916d078a5ccf97\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aec37582522615c6d6916d078a5ccf97\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4dccb56f227b56afcf95779216ccc46\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4dccb56f227b56afcf95779216ccc46\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd5de746ab9f324291c45c4e18d466e0\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd5de746ab9f324291c45c4e18d466e0\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c37758e4c632d9b51d3452f8c90e3\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\831c37758e4c632d9b51d3452f8c90e3\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44aaec257ce2cc8cac45c8fbfc80c0\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a44aaec257ce2cc8cac45c8fbfc80c0\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e96e792996caa8e95571065620321b6\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6d4b9ab294dc37ddcdb38115157bb74\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6d4b9ab294dc37ddcdb38115157bb74\transformed\jetified-play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d238ff801bf4159163d36044ed821d83\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d238ff801bf4159163d36044ed821d83\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44705c864210af715dcb07f73a96c9ba\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44705c864210af715dcb07f73a96c9ba\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f34f03ecdac974155dc3e7c955360db0\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f34f03ecdac974155dc3e7c955360db0\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4a95987497762ed3af885ab116fa264\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4a95987497762ed3af885ab116fa264\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc9e89cb1ff62871c83c62856bf97ce5\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc9e89cb1ff62871c83c62856bf97ce5\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\932ba72e17c40aa8556c758dac8079d3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\932ba72e17c40aa8556c758dac8079d3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a45c0271b394df3a37de6cccf30579d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a45c0271b394df3a37de6cccf30579d\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5177fdbad1250f9870a2fbb337983ac8\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5177fdbad1250f9870a2fbb337983ac8\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c79ad1cc2e89996ad18f6440f6c925\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c79ad1cc2e89996ad18f6440f6c925\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4940c476a92c0e1ce6707ed6d5a3fab1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4940c476a92c0e1ce6707ed6d5a3fab1\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7643d420cb7f0d645ee669af01e246f4\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7643d420cb7f0d645ee669af01e246f4\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9744ea7d593db4848481b2dc1ca0ed99\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9744ea7d593db4848481b2dc1ca0ed99\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dad94121f24fbe5d0b95c09c16583fa\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1dad94121f24fbe5d0b95c09c16583fa\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\861b4fa0657264fccfc92d357e99c12c\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\861b4fa0657264fccfc92d357e99c12c\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6580ba3899d752f0adcab81246e19f4\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6580ba3899d752f0adcab81246e19f4\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17380c18e82b42e87b2d6dfc71715bd4\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17380c18e82b42e87b2d6dfc71715bd4\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20044f6c5f7a971de13e8ca5eb00b3f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20044f6c5f7a971de13e8ca5eb00b3f\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f0aab3e896844600ee839129612136c\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f0aab3e896844600ee839129612136c\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b6ac2e46b48ac9a3aa0ffb25549f7d\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5b6ac2e46b48ac9a3aa0ffb25549f7d\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d145a5b15b5ab9079a5c742530e158\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08d145a5b15b5ab9079a5c742530e158\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6156536e0b56b649058e060194ae134e\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6156536e0b56b649058e060194ae134e\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06934b7ee87ba902cc1c97beba47907c\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06934b7ee87ba902cc1c97beba47907c\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\734287ca7a5c3cecf4ed3a5d2b4dd2a4\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\734287ca7a5c3cecf4ed3a5d2b4dd2a4\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aeb39eea497f4247ef3d1f00500f89\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21aeb39eea497f4247ef3d1f00500f89\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e37d8cd2361f3119cb59f8003d0868\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e37d8cd2361f3119cb59f8003d0868\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dad88896bac44393d0c09f988f7f33f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dad88896bac44393d0c09f988f7f33f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f480edc4ef2de9e546b75a25dbb76874\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f480edc4ef2de9e546b75a25dbb76874\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71741ae3608a014bf71abddc3430cd76\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71741ae3608a014bf71abddc3430cd76\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4017ee38fffd86d5d2bd1c1bd1be11c8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4017ee38fffd86d5d2bd1c1bd1be11c8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0780b40bae4a82b2ae549deaa1d2d7a\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0780b40bae4a82b2ae549deaa1d2d7a\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae06639c40c37163a8dbcecf01e73766\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae06639c40c37163a8dbcecf01e73766\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e029052ced966115f4d17af0300d534\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e029052ced966115f4d17af0300d534\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83284b1ca1734e1de536bf196acf215e\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83284b1ca1734e1de536bf196acf215e\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc5c87a03af947d6c14df0602dee0471\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc5c87a03af947d6c14df0602dee0471\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e49c3ea676e22abc40aa6676849933c4\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e49c3ea676e22abc40aa6676849933c4\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591a2f912f2642b7c3ce93cb5c31cb2\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a591a2f912f2642b7c3ce93cb5c31cb2\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db920e7c324fc81d84865df5ad7793be\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db920e7c324fc81d84865df5ad7793be\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3b47443e0758c24ab25e1a567f9ffd\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b3b47443e0758c24ab25e1a567f9ffd\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aebe4dd8179f1aa94b7c979bec7ed9c5\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aebe4dd8179f1aa94b7c979bec7ed9c5\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4c7bd69a33da4ec1eccd1f1916102f3\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4c7bd69a33da4ec1eccd1f1916102f3\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb45c187cc4bf07f326acb55403dced5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb45c187cc4bf07f326acb55403dced5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0f9724190e07e8737aaa267698c9de2\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0f9724190e07e8737aaa267698c9de2\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e083c77bf2774c552dcb395d84f66a5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e083c77bf2774c552dcb395d84f66a5\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0f12582c452eb691a8e44b0e8084bba\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0f12582c452eb691a8e44b0e8084bba\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8933da7794439b520a0a7d7c44b67732\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8933da7794439b520a0a7d7c44b67732\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f9f62d475da5647ccd554e09d9a5175\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f9f62d475da5647ccd554e09d9a5175\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb23a5c46c3e34a7c7847fc03bf5fc3c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb23a5c46c3e34a7c7847fc03bf5fc3c\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98de64f0111d22d1533700e71a117eb1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98de64f0111d22d1533700e71a117eb1\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97bc3aba17b8e77b2a7cadcc02faec9f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97bc3aba17b8e77b2a7cadcc02faec9f\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\384a3487ef0a0080c58695a386ab607a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\384a3487ef0a0080c58695a386ab607a\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37de469302e2c446aa8c642cc10d3877\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37de469302e2c446aa8c642cc10d3877\transformed\jetified-emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d60b9dfcc626ec6500a5bf1bb597609c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d60b9dfcc626ec6500a5bf1bb597609c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da03bed7a3bc1708d07957d8daac4d73\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da03bed7a3bc1708d07957d8daac4d73\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2e5b5c6f8d61eda7736195ad612c0c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff2e5b5c6f8d61eda7736195ad612c0c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9ae86c85487d86163dc59b9985eb1b1\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e9ae86c85487d86163dc59b9985eb1b1\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\876e4ff582ebdfeddfd9bca2d49466dd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\876e4ff582ebdfeddfd9bca2d49466dd\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b160d8b9da1779072233b65dafb67e1\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b160d8b9da1779072233b65dafb67e1\transformed\jetified-datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adbfae24b25496a769a0de168ef10463\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adbfae24b25496a769a0de168ef10463\transformed\jetified-datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbddd8ac3be61af4933f58e5538a8bdb\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbddd8ac3be61af4933f58e5538a8bdb\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfe8c73686adb669489e2b68431f1479\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfe8c73686adb669489e2b68431f1479\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e05370dafb4c7ac1c53ece1b10c40dc\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e05370dafb4c7ac1c53ece1b10c40dc\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11143826e5a29537e8ebfb75c1f165fa\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11143826e5a29537e8ebfb75c1f165fa\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8a00346f769e30fcf102afe4b03d0df\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8a00346f769e30fcf102afe4b03d0df\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b2a6a802b80116484a4d228fbeb1a77\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b2a6a802b80116484a4d228fbeb1a77\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d36266a896a5865f523cbd5bb0163b0\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d36266a896a5865f523cbd5bb0163b0\transformed\jetified-play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f9e9b6b52770bd44d0b075f702cb31a\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f9e9b6b52770bd44d0b075f702cb31a\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29be1fa7446a69f224d9ea40229e93c\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f29be1fa7446a69f224d9ea40229e93c\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\260522677f8d78cc30a779d048c78623\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\260522677f8d78cc30a779d048c78623\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c11d81441550d0689fe1a67fbc28a1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1c11d81441550d0689fe1a67fbc28a1\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a624f6169bd423a64f257c4af3ec9f18\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a624f6169bd423a64f257c4af3ec9f18\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44734e41a66c83c09439a2ee65352add\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44734e41a66c83c09439a2ee65352add\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\297d281f214de12afec0ec6772c222bb\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\297d281f214de12afec0ec6772c222bb\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b577e9390ae7b35b2264246136ac5c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b577e9390ae7b35b2264246136ac5c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c1b45a514dca3fc75b0b1bcc975036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21c1b45a514dca3fc75b0b1bcc975036\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\809f7685cfffa5bfe4e206b55f99aafe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\809f7685cfffa5bfe4e206b55f99aafe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad783780d62bb70d3b31349f8da5d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dad783780d62bb70d3b31349f8da5d7\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f366e0976d4008642dc16217a8811b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f366e0976d4008642dc16217a8811b8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a13a77dd6226e0f03c0adc904d93bab0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a13a77dd6226e0f03c0adc904d93bab0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d3e3f1bd8a48a8aaf1db8287bb1341\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0d3e3f1bd8a48a8aaf1db8287bb1341\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe3d372fd19dedb11f4738343e5decc2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe3d372fd19dedb11f4738343e5decc2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d04fca7f6da8dbd8e5a516a5e295a7af\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d04fca7f6da8dbd8e5a516a5e295a7af\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27abe7c8fa32e05315b9b266c81703d4\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27abe7c8fa32e05315b9b266c81703d4\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d0c7249b1d25e81d28d5842176966cc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d0c7249b1d25e81d28d5842176966cc\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06aa85e04b36544d68a4bc5515558aa9\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06aa85e04b36544d68a4bc5515558aa9\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ae0dd42f241a9bccdb2073f11a3660\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ae0dd42f241a9bccdb2073f11a3660\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a74ca4ad728806d1f5c26d377000afdb\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a74ca4ad728806d1f5c26d377000afdb\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1027c7af5ad42434485bf3858f32dcf4\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1027c7af5ad42434485bf3858f32dcf4\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d2725a8f52f2f1b7f5df24dbfcf06b9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d2725a8f52f2f1b7f5df24dbfcf06b9\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82370e92982c022c5183cf441d1dd675\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82370e92982c022c5183cf441d1dd675\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a530d30d38b7060f780b584f0324ba6b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a530d30d38b7060f780b584f0324ba6b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebdaa7a64fa10d06136ca743315eaa1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ebdaa7a64fa10d06136ca743315eaa1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb57c3e5a03a182263a89edddce573a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb57c3e5a03a182263a89edddce573a7\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55bd33645caf703ea55f1d1f4816c5f8\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55bd33645caf703ea55f1d1f4816c5f8\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54ac26a17e1996f67c0683b84824bbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a54ac26a17e1996f67c0683b84824bbf\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a54714422927f398c3d7384d486368e\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a54714422927f398c3d7384d486368e\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f543ec7bf2ab950372c7a56e243d0a81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f543ec7bf2ab950372c7a56e243d0a81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d9e0c9d265525bc50843502f561d93\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d9e0c9d265525bc50843502f561d93\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caccccc897ccf3cd2ffaeaa238014d9f\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\caccccc897ccf3cd2ffaeaa238014d9f\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.chuckerteam.chucker:library-no-op:3.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b27b4ed41d3e76df5dbae1c75e8e9d8e\transformed\jetified-library-no-op-3.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.chuckerteam.chucker:library-no-op:3.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b27b4ed41d3e76df5dbae1c75e8e9d8e\transformed\jetified-library-no-op-3.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db0890d196505bd23acf50fce22364ef\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db0890d196505bd23acf50fce22364ef\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3217727316632b44892913484b9635df\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3217727316632b44892913484b9635df\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.midtrans:issuetracker:1.12.0-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4618d46e7fa1268b54a29fd46ce2b41c\transformed\jetified-issuetracker-1.12.0-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [com.midtrans:issuetracker:1.12.0-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4618d46e7fa1268b54a29fd46ce2b41c\transformed\jetified-issuetracker-1.12.0-SANDBOX\AndroidManifest.xml:7:5-9:41
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6a794da9e3a75be32e0bfebe1ec73a5\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6a794da9e3a75be32e0bfebe1ec73a5\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c00d41ccda6e3300e348397243de06d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c00d41ccda6e3300e348397243de06d\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea45bcc7faaae4cfa3a616b9f8202ae6\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea45bcc7faaae4cfa3a616b9f8202ae6\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [me.saket:better-link-movement-method:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf62539f90a6d2a15f0bacfd65e04f94\transformed\jetified-better-link-movement-method-2.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [me.saket:better-link-movement-method:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf62539f90a6d2a15f0bacfd65e04f94\transformed\jetified-better-link-movement-method-2.2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0bdcec605fd268c06eebe371cd0f6d0c\transformed\jetified-androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\wamp64\www\user_app\android\app\src\debug\AndroidManifest.xml
activity#co.paystack.flutterpaystack.AuthActivity
ADDED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:54
	android:theme
		ADDED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-51
	android:name
		ADDED from [:flutter_paystack] C:\wamp64\www\user_app\build\flutter_paystack\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-68
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\wamp64\www\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\wamp64\www\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\wamp64\www\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_crashlytics] C:\wamp64\www\user_app\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar
ADDED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_database] C:\wamp64\www\user_app\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\wamp64\www\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] C:\wamp64\www\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\wamp64\www\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d2a0ca0871e124bb43a68de5876953\transformed\jetified-firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8190165eda06f1f234910c2485f95aa\transformed\jetified-firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6119b52f58939b8879ef49de90092bff\transformed\jetified-firebase-crashlytics-19.3.0\AndroidManifest.xml:19:17-115
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f173bbed52099cc8883b0bf7c54be1\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4659d2c4e10ce5f4aa5b71f177d7f67\transformed\jetified-play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6040f2b26491b2b6cd22a1c881b52879\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eecafd2c4a9856dc34f8d52d561704\transformed\jetified-firebase-sessions-2.0.7\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1ed931b380d81884599d3d71fb975de\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\917b18cebdca51ba316072240d4f7295\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1128e7f6a268cd16501f7c157f165ae0\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00174972cc98aa755664ae6ef3dfe23d\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b320831c9a57ef5dd4f04e1944c12dc\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cb861a576430351f576730e7c7f57ef\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee19b07e5655d40f0fa8a3cd947c987f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0ce42aac1195502071bfed615d5f31d\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
queries
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:16:5-18:15
MERGED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:16:5-18:15
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:7:5-27:15
MERGED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:7:5-27:15
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:8:5-12:15
MERGED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-17:15
MERGED from [com.github.droibit:customtabslauncher:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2fefeb540c70b1e1cc75182ca7c52c\transformed\jetified-customtabslauncher-2.0.0\AndroidManifest.xml:7:5-11:15
MERGED from [com.github.droibit:customtabslauncher:2.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f2fefeb540c70b1e1cc75182ca7c52c\transformed\jetified-customtabslauncher-2.0.0\AndroidManifest.xml:7:5-11:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] C:\wamp64\www\user_app\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
uses-permission#android.permission.READ_PHONE_STATE
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:14:5-75
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:13:5-75
MERGED from [com.midtrans:analytics:1.7.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bceaab446637185569c2f6915ee12ebf\transformed\jetified-analytics-1.7.1-SANDBOX\AndroidManifest.xml:13:5-75
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:14:22-72
package#com.gojek.app
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:17:9-49
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:17:18-46
activity#com.midtrans.sdk.uikit.activities.UserDetailsActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:24:9-27:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:26:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:27:13-64
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:25:13-81
activity#com.midtrans.sdk.uikit.activities.PaymentMethodsActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:28:9-31:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:31:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:30:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:29:13-84
activity#com.midtrans.sdk.uikit.views.mandiri_clickpay.MandiriClickPayActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:32:9-36:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:35:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:36:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:34:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:33:13-97
activity#com.midtrans.sdk.uikit.views.bri_epay.BriEpayPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:37:9-40:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:40:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:39:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:38:13-88
activity#com.midtrans.sdk.uikit.views.cimb_click.CimbClickPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:41:9-44:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:44:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:43:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:42:13-92
activity#com.midtrans.sdk.uikit.views.mandiri_ecash.MandiriEcashPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:45:9-48:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:48:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:47:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:46:13-98
activity#com.midtrans.sdk.uikit.views.indosat_dompetku.IndosatDompetkuPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:49:9-53:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:52:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:53:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:51:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:50:13-104
activity#com.midtrans.sdk.uikit.views.indomaret.payment.IndomaretPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:54:9-57:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:57:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:56:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:55:13-99
activity#com.midtrans.sdk.uikit.views.indomaret.status.IndomaretStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:58:9-61:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:61:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:60:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:59:13-97
activity#com.midtrans.sdk.uikit.views.telkomsel_cash.TelkomselCashPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:62:9-66:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:65:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:66:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:64:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:63:13-100
activity#com.midtrans.sdk.uikit.views.xl_tunai.payment.XlTunaiPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:67:9-70:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:70:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:68:13-96
activity#com.midtrans.sdk.uikit.views.xl_tunai.status.XlTunaiStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:71:9-74:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:74:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:72:13-94
activity#com.midtrans.sdk.uikit.views.xl_tunai.XlTunaiInstructionActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:75:9-78:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:78:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:77:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:76:13-92
activity#com.midtrans.sdk.uikit.views.kioson.payment.KiosonPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:79:9-82:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:82:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:81:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:80:13-93
activity#com.midtrans.sdk.uikit.views.kioson.status.KiosonStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:83:9-86:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:86:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:85:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:84:13-91
activity#com.midtrans.sdk.uikit.views.gci.GciPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:87:9-91:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:90:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:91:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:89:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:88:13-79
activity#com.midtrans.sdk.uikit.views.creditcard.details.CreditCardDetailsActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:92:9-96:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:95:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:96:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:94:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:93:13-101
activity#com.midtrans.sdk.uikit.views.status.PaymentStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:97:9-100:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:100:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:99:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:98:13-85
activity#com.midtrans.sdk.uikit.views.creditcard.saved.SavedCreditCardActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:101:9-104:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:104:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:103:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:102:13-97
activity#com.midtrans.sdk.uikit.views.creditcard.bankpoints.BankPointsActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:105:9-109:76
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:108:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:109:13-73
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:107:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:106:13-97
activity#com.midtrans.sdk.uikit.views.banktransfer.list.BankTransferListActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:110:9-113:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:113:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:112:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:111:13-99
activity#com.midtrans.sdk.uikit.views.banktransfer.payment.BankTransferPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:114:9-118:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:117:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:118:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:116:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:115:13-105
activity#com.midtrans.sdk.uikit.views.banktransfer.status.VaPaymentStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:119:9-122:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:122:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:121:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:120:13-100
activity#com.midtrans.sdk.uikit.views.banktransfer.status.VaOtherBankPaymentStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:123:9-126:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:126:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:125:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:124:13-109
activity#com.midtrans.sdk.uikit.views.banktransfer.status.MandiriBillStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:127:9-130:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:130:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:129:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:128:13-102
activity#com.midtrans.sdk.uikit.views.creditcard.register.CardRegistrationActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:131:9-135:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:134:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:135:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:133:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:132:13-101
activity#com.midtrans.sdk.uikit.views.gopay.payment.GoPayPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:136:9-139:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:139:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:138:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:137:13-91
activity#com.midtrans.sdk.uikit.views.shopeepay.payment.ShopeePayPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:140:9-143:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:143:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:142:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:141:13-99
activity#com.midtrans.sdk.uikit.views.gopay.status.GoPayStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:144:9-147:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:147:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:146:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:145:13-89
activity#com.midtrans.sdk.uikit.views.shopeepay.status.ShopeePayStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:148:9-151:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:151:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:150:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:149:13-97
activity#com.midtrans.sdk.uikit.views.danamon_online.DanamonOnlineActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:152:9-155:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:155:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:154:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:153:13-93
activity#com.midtrans.sdk.uikit.views.bca_klikbca.payment.KlikBcaPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:156:9-160:67
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:159:13-49
	android:windowSoftInputMode
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:160:13-64
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:158:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:157:13-99
activity#com.midtrans.sdk.uikit.views.bca_klikbca.status.KlikBcaStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:161:9-164:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:164:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:163:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:162:13-97
activity#com.midtrans.sdk.uikit.views.bca_klikpay.BcaKlikPayPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:165:9-168:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:168:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:167:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:166:13-94
activity#com.midtrans.sdk.uikit.views.alfamart.payment.AlfamartPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:169:9-172:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:172:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:171:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:170:13-97
activity#com.midtrans.sdk.uikit.views.alfamart.status.AlfamartStatusActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:173:9-176:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:176:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:175:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:174:13-95
activity#com.midtrans.sdk.uikit.views.webview.WebViewPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:177:9-180:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:180:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:179:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:178:13-87
activity#com.midtrans.sdk.uikit.views.creditcard.tnc.TermsAndConditionsActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:181:9-184:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:184:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:183:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:182:13-98
activity#com.midtrans.sdk.uikit.views.akulaku.AkulakuActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:185:9-188:52
	android:screenOrientation
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:188:13-49
	android:configChanges
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:187:13-74
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:186:13-80
activity#com.midtrans.sdk.uikit.views.uob.UobListActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:189:9-85
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:189:19-82
activity#com.midtrans.sdk.uikit.views.uob.app.UobAppPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:190:9-95
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:190:19-92
activity#com.midtrans.sdk.uikit.views.uob.web.UobWebPaymentActivity
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:191:9-95
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:191:19-92
service#com.midtrans.raygun.RaygunPostService
ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:193:9-196:52
	android:process
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:196:13-49
	android:exported
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:195:13-37
	android:name
		ADDED from [com.midtrans:uikit:1.31.1-SANDBOX] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22bbc9bab209556a742f74d50540c114\transformed\jetified-uikit-1.31.1-SANDBOX\AndroidManifest.xml:194:13-65
package#com.phonepe.app
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:8:9-51
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:8:18-48
package#com.phonepe.app.preprod
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:9:9-59
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:9:18-56
package#com.phonepe.simulator
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:10:9-57
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:10:18-54
package#com.phonepe.simulator.debug
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:11:9-63
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:11:18-60
intent#action:name:android.intent.action.VIEW+data:host:pay+data:scheme:upi
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:13:9-19:18
intent#action:name:android.intent.action.VIEW+data:host:upi+data:scheme:mandate
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:20:9-26:18
meta-data#android.webkit.WebView.EnableSafeBrowsing
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:32:5-34:32
	android:value
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:34:9-29
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:33:9-65
activity#com.phonepe.intent.sdk.ui.B2BPGActivity
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:37:9-40:66
	android:exported
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:39:13-37
	android:theme
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:40:13-63
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:38:13-67
activity#com.phonepe.intent.sdk.ui.TransactionActivity
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:41:9-45:67
	android:windowSoftInputMode
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:45:13-64
	android:configChanges
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:43:13-48
	android:theme
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:44:13-63
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:42:13-73
service#com.phonepe.intent.sdk.ui.PreCacheService
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:49:13-37
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:48:13-69
activity#com.phonepe.intent.sdk.ui.OpenIntentTransactionActivity
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:51:9-53:66
	android:theme
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:53:13-63
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:52:13-83
activity#com.phonepe.intent.sdk.ui.UpiAppsSelectionDialogActivity
ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:54:9-56:60
	android:theme
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:56:13-57
	android:name
		ADDED from [phonepe.intentsdk.android.release:IntentSDK:2.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c579333f0dbea1cc97c076fff35d656\transformed\jetified-IntentSDK-2.4.2\AndroidManifest.xml:55:13-84
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:13:9-17:18
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.nafiss.user/cancel+data:path:/com.nafiss.user/success+data:pathPrefix:/com.nafiss.user+data:pathPrefix:/com.nafiss.user+data:pathPrefix:/com.nafiss.user/authentication_return+data:pathPrefix:/com.nafiss.user/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55b04eea67718aa5f042d71528c48708\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:71:13-110
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2da40362deff4591e4d0e8735fd0a8f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:41:13-121
activity#com.stripe.android.link.LinkActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:8:9-13:61
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:12:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:10:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:11:13-115
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:13:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:9:13-64
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:14:9-19:61
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:18:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:16:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:17:13-115
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:19:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:15:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:20:9-37:20
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:24:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:22:13-49
	android:exported
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:25:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:21:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.nafiss.user+data:scheme:link-popup
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411434f15fbcff9befcd2c1cf22cdcf\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b7797762919ee15e445780966e20543\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.AddPaymentMethodActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:16:13-76
activity#com.stripe.android.view.PaymentMethodsActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:19:9-22:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:22:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:20:13-74
activity#com.stripe.android.view.PaymentFlowActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:23:9-26:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:26:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:24:13-71
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:27:9-30:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:30:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:28:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:31:9-34:61
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:33:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:34:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:32:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:40:9-44:61
	android:launchMode
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:43:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:44:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:41:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:45:9-62:20
	android:launchMode
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:48:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:47:13-36
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:49:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:46:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.nafiss.user+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:63:9-66:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:66:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:64:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:67:9-70:66
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:70:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:68:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:71:9-74:66
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:73:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:74:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:72:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:75:9-78:68
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:77:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:78:13-65
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:76:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:79:9-82:61
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:82:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed20371544067e08c9011c623f155332\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:80:13-97
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8f1b710ea72e98967c6072cb046dd43c\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\wamp64\www\user_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-35
	android:name
		ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-64
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] C:\wamp64\www\user_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:21-69
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] C:\wamp64\www\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\wamp64\www\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\wamp64\www\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-feature#android.hardware.camera
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:24:5-26:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:26:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:25:9-47
uses-feature#android.hardware.camera.front
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:29:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:28:9-53
uses-feature#android.hardware.camera.autofocus
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:32:5-34:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:34:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:33:9-57
uses-feature#android.hardware.camera.flash
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:35:5-37:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:37:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:36:9-53
uses-feature#android.hardware.screen.landscape
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:38:5-40:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:40:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:39:9-57
uses-feature#android.hardware.wifi
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:41:5-43:36
	android:required
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:43:9-33
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:42:9-45
activity#com.journeyapps.barcodescanner.CaptureActivity
ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:46:9-52:63
	android:screenOrientation
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:49:13-56
	android:clearTaskOnLaunch
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:48:13-45
	android:stateNotNeeded
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:50:13-42
	android:windowSoftInputMode
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:52:13-60
	android:theme
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:51:13-54
	android:name
		ADDED from [com.journeyapps:zxing-android-embedded:3.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f4b43e690c05ab4f64782d5e6805b\transformed\jetified-zxing-android-embedded-3.5.0\AndroidManifest.xml:47:13-74
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ed5f7aa94f8f0085aefd9a5237d758\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26b716dd4ce231a28c886a6457744867\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5b11a3b9968ffe85a723b18aaab3a8e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f33ffb165e87ac280eec92e7263daf01\transformed\jetified-play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df4ecf3bde18b9a00ebaef3c4610c71\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f87a2a6f74cb89749efcaec1733d351e\transformed\jetified-recaptcha-18.5.1\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c463d01c02249612487e6b7dcc8c58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\619d37a08950e62ae220e67a312aa41a\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54f8a3ad7875ac8ead281ec3656f2d1c\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\605c3bef1c4859f5902af1e31462cde4\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef9ea62e2e1d295a90fe1d76c41355d7\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7641d5886647657d0030d119d80c30f\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5afa80997a1378b0216f881e286516d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483091e0b4c7d0c83ff9b4e39e422f52\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.nafiss.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59d0c815c7d39ad0cf5643a81ed8f019\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7411a5828c70b57008080005a3e29697\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4ae60f416345ab1cbed86f152a65906\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7f37a25e2aab39bf306a34a9a4ba8ca\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\047d8895894c818443afcee067701a9a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cbf949ef49967cb45597c658ed2054f3\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1049c57dc43877913622bfca54ba0aa3\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
